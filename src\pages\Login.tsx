import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Mail, Lock, Eye, EyeOff, Shield, User } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { showToast } from '../components/Toast';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);

  const [loginType, setLoginType] = useState<'customer' | 'admin'>('customer');

  const { login, isLoading, error, clearError } = useAuthStore();
  const navigate = useNavigate();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    clearError(); // Clear error when user starts typing
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await login(formData.email, formData.password);

      showToast.success('Login Successful', 'Welcome back to QuickDrop!');

      // Navigation will be handled by the ProtectedRoute component
      // based on user role
      navigate('/dashboard');
    } catch (err) {
      showToast.error('Login Failed', 'Invalid email or password. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-800">Welcome Back</h2>
          <p className="mt-2 text-gray-600">Sign in to your QuickDrop account</p>
        </div>

        {/* Login Type Selector */}
        <div className="bg-white rounded-xl shadow-lg p-2 border border-gray-100">
          <div className="grid grid-cols-2 gap-1">
            <button
              type="button"
              onClick={() => setLoginType('customer')}
              className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium text-sm transition-all duration-200 ${
                loginType === 'customer'
                  ? 'bg-blue-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <User className="h-4 w-4" />
              <span>Customer</span>
            </button>
            <button
              type="button"
              onClick={() => setLoginType('admin')}
              className={`flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium text-sm transition-all duration-200 ${
                loginType === 'admin'
                  ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <Shield className="h-4 w-4" />
              <span>Admin</span>
            </button>
          </div>
        </div>

        <div className={`bg-white rounded-xl shadow-lg p-8 border ${
          loginType === 'admin' ? 'border-purple-200' : 'border-gray-100'
        }`}>
          {loginType === 'admin' && (
            <div className="mb-6 p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
              <div className="flex items-center space-x-2 mb-2">
                <Shield className="h-5 w-5 text-purple-600" />
                <span className="font-semibold text-purple-800">Admin Access</span>
              </div>
              <p className="text-sm text-purple-700">
                Secure administrative portal for managing delivery operations, partners, and analytics.
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all ${
                    loginType === 'admin'
                      ? 'border-purple-300 focus:ring-purple-500'
                      : 'border-gray-300 focus:ring-blue-500'
                  }`}
                  placeholder={loginType === 'admin' ? '<EMAIL>' : 'Enter your email'}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className={`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:border-transparent transition-all ${
                    loginType === 'admin'
                      ? 'border-purple-300 focus:ring-purple-500'
                      : 'border-gray-300 focus:ring-blue-500'
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <label className="flex items-center">
                <input type="checkbox" className="rounded border-gray-300 text-blue-600 mr-2" />
                <span className="text-sm text-gray-600">Remember me</span>
              </label>
              <Link to="#" className="text-sm text-blue-600 hover:text-blue-700">
                Forgot password?
              </Link>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className={`w-full py-3 rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 flex items-center justify-center space-x-2 ${
                loginType === 'admin'
                  ? 'bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 shadow-lg'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Signing In...</span>
                </>
              ) : (
                <>
                  {loginType === 'admin' ? <Shield className="h-5 w-5" /> : <User className="h-5 w-5" />}
                  <span>Sign In as {loginType === 'admin' ? 'Admin' : 'Customer'}</span>
                </>
              )}
            </button>
          </form>

          {loginType === 'customer' && (
            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Don't have an account?{' '}
                <Link to="/register" className="text-blue-600 hover:text-blue-700 font-semibold">
                  Sign Up
                </Link>
              </p>
            </div>
          )}

          {/* Demo Credentials */}
          <div className={`mt-6 p-4 rounded-lg ${
            loginType === 'admin' 
              ? 'bg-purple-50 border border-purple-200' 
              : 'bg-blue-50 border border-blue-200'
          }`}>
            <p className={`text-sm font-semibold mb-2 ${
              loginType === 'admin' ? 'text-purple-800' : 'text-blue-800'
            }`}>
              Demo Credentials:
            </p>
            <div className={`text-sm space-y-1 ${
              loginType === 'admin' ? 'text-purple-700' : 'text-blue-700'
            }`}>
              {loginType === 'admin' ? (
                <>
                  <p>Admin: <EMAIL> / password</p>
                  <p>Super Admin: <EMAIL> / password</p>
                </>
              ) : (
                <>
                  <p>Customer: <EMAIL> / password</p>
                  <p>User: <EMAIL> / password</p>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;