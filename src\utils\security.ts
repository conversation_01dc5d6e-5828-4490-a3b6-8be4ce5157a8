// Security utilities and configurations

// Content Security Policy configuration
export const CSP_CONFIG = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Required for Vite in development
    "'unsafe-eval'", // Required for Vite in development
    "https://unpkg.com", // For Leaflet
    "https://cdn.jsdelivr.net" // For external libraries
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for styled-components and CSS-in-JS
    "https://unpkg.com", // For Leaflet CSS
    "https://fonts.googleapis.com"
  ],
  'img-src': [
    "'self'",
    "data:",
    "blob:",
    "https://*.tile.openstreetmap.org", // For map tiles
    "https://unpkg.com" // For Leaflet images
  ],
  'font-src': [
    "'self'",
    "https://fonts.gstatic.com"
  ],
  'connect-src': [
    "'self'",
    "https://api.quickdrop.com", // Your API endpoint
    "wss://api.quickdrop.com", // WebSocket endpoint
    "https://*.tile.openstreetmap.org" // For map data
  ],
  'frame-src': ["'none'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
  'upgrade-insecure-requests': []
};

// Generate CSP header string
export const generateCSPHeader = (): string => {
  return Object.entries(CSP_CONFIG)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive;
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
};

// Security headers configuration
export const SECURITY_HEADERS = {
  'Content-Security-Policy': generateCSPHeader(),
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(self)',
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

// Apply security headers (for development testing)
export const applySecurityHeaders = () => {
  if (typeof document !== 'undefined') {
    // Create meta tags for CSP (fallback for development)
    const cspMeta = document.createElement('meta');
    cspMeta.httpEquiv = 'Content-Security-Policy';
    cspMeta.content = generateCSPHeader();
    
    // Only add if not already present
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      document.head.appendChild(cspMeta);
    }
  }
};

// Input sanitization for XSS prevention
export const sanitizeForXSS = (input: string): string => {
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
};

// CSRF token management
class CSRFTokenManager {
  private token: string | null = null;
  private readonly TOKEN_KEY = 'csrf_token';

  generateToken(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    this.token = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    
    // Store in sessionStorage for SPA
    sessionStorage.setItem(this.TOKEN_KEY, this.token);
    return this.token;
  }

  getToken(): string | null {
    if (!this.token) {
      this.token = sessionStorage.getItem(this.TOKEN_KEY);
    }
    return this.token;
  }

  validateToken(token: string): boolean {
    const storedToken = this.getToken();
    return storedToken !== null && storedToken === token;
  }

  clearToken(): void {
    this.token = null;
    sessionStorage.removeItem(this.TOKEN_KEY);
  }
}

export const csrfTokenManager = new CSRFTokenManager();

// Rate limiting for client-side
class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  isAllowed(key: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now();
    const requests = this.requests.get(key) || [];
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      return false;
    }
    
    validRequests.push(now);
    this.requests.set(key, validRequests);
    return true;
  }

  reset(key: string): void {
    this.requests.delete(key);
  }
}

export const rateLimiter = new RateLimiter();

// Secure storage utilities
export const secureStorage = {
  // Encrypt data before storing (basic implementation)
  setItem: (key: string, value: string, encrypt: boolean = false): void => {
    try {
      const dataToStore = encrypt ? btoa(value) : value;
      localStorage.setItem(key, dataToStore);
    } catch (error) {
      console.error('Failed to store data:', error);
    }
  },

  getItem: (key: string, decrypt: boolean = false): string | null => {
    try {
      const data = localStorage.getItem(key);
      if (!data) return null;
      return decrypt ? atob(data) : data;
    } catch (error) {
      console.error('Failed to retrieve data:', error);
      return null;
    }
  },

  removeItem: (key: string): void => {
    localStorage.removeItem(key);
  },

  // Clear sensitive data
  clearSensitiveData: (): void => {
    const sensitiveKeys = ['auth_token', 'user_data', 'csrf_token'];
    sensitiveKeys.forEach(key => {
      localStorage.removeItem(key);
      sessionStorage.removeItem(key);
    });
  }
};

// Security event monitoring
export class SecurityMonitor {
  private static instance: SecurityMonitor;
  private events: Array<{ type: string; timestamp: number; details: any }> = [];

  static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor();
    }
    return SecurityMonitor.instance;
  }

  logSecurityEvent(type: string, details: any = {}): void {
    const event = {
      type,
      timestamp: Date.now(),
      details: {
        ...details,
        userAgent: navigator.userAgent,
        url: window.location.href
      }
    };

    this.events.push(event);
    
    // Keep only last 100 events
    if (this.events.length > 100) {
      this.events = this.events.slice(-100);
    }

    // Log critical events
    if (this.isCriticalEvent(type)) {
      console.warn('Security Event:', event);
      // In production, send to security monitoring service
      this.reportToSecurityService(event);
    }
  }

  private isCriticalEvent(type: string): boolean {
    const criticalEvents = [
      'xss_attempt',
      'csrf_token_mismatch',
      'rate_limit_exceeded',
      'suspicious_input',
      'authentication_failure'
    ];
    return criticalEvents.includes(type);
  }

  private reportToSecurityService(event: any): void {
    // In production, send to your security monitoring service
    // Example: Sentry, LogRocket, or custom endpoint
    console.log('Would report to security service:', event);
  }

  getEvents(): Array<{ type: string; timestamp: number; details: any }> {
    return [...this.events];
  }

  clearEvents(): void {
    this.events = [];
  }
}

export const securityMonitor = SecurityMonitor.getInstance();

// Detect and prevent common attacks
export const securityChecks = {
  // Check for XSS attempts
  detectXSS: (input: string): boolean => {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe/gi,
      /<object/gi,
      /<embed/gi,
      /eval\s*\(/gi,
      /expression\s*\(/gi
    ];

    const hasXSS = xssPatterns.some(pattern => pattern.test(input));
    
    if (hasXSS) {
      securityMonitor.logSecurityEvent('xss_attempt', { input });
    }
    
    return hasXSS;
  },

  // Check for SQL injection attempts
  detectSQLInjection: (input: string): boolean => {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(--|\/\*|\*\/)/g,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/gi
    ];

    const hasSQLInjection = sqlPatterns.some(pattern => pattern.test(input));
    
    if (hasSQLInjection) {
      securityMonitor.logSecurityEvent('sql_injection_attempt', { input });
    }
    
    return hasSQLInjection;
  },

  // Validate file uploads
  validateFileUpload: (file: File): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      errors.push('File type not allowed');
    }

    if (file.size > maxSize) {
      errors.push('File size too large');
    }

    // Check for suspicious file names
    if (/\.(exe|bat|cmd|scr|pif|com)$/i.test(file.name)) {
      errors.push('Suspicious file extension');
      securityMonitor.logSecurityEvent('suspicious_file_upload', { 
        fileName: file.name, 
        fileType: file.type 
      });
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

// Initialize security measures
export const initializeSecurity = (): void => {
  // Apply security headers
  applySecurityHeaders();

  // Generate initial CSRF token
  csrfTokenManager.generateToken();

  // Monitor for security events
  window.addEventListener('error', (event) => {
    securityMonitor.logSecurityEvent('javascript_error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno
    });
  });

  // Monitor for unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    securityMonitor.logSecurityEvent('unhandled_rejection', {
      reason: event.reason
    });
  });

  console.log('Security measures initialized');
};
