import React, { useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, TrendingDown, Package, Clock, DollarSign, Users, Calendar, Filter } from 'lucide-react';
import { motion } from 'framer-motion';

interface AnalyticsData {
  deliveries: {
    total: number;
    completed: number;
    pending: number;
    cancelled: number;
    growth: number;
  };
  revenue: {
    total: number;
    thisMonth: number;
    lastMonth: number;
    growth: number;
  };
  performance: {
    avgDeliveryTime: number;
    onTimeRate: number;
    customerSatisfaction: number;
  };
  trends: {
    daily: Array<{ date: string; deliveries: number; revenue: number }>;
    monthly: Array<{ month: string; deliveries: number; revenue: number }>;
  };
}

interface AnalyticsDashboardProps {
  data?: AnalyticsData;
  timeRange?: '7d' | '30d' | '90d' | '1y';
  onTimeRangeChange?: (range: '7d' | '30d' | '90d' | '1y') => void;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({
  data,
  timeRange = '30d',
  onTimeRangeChange
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'deliveries' | 'revenue' | 'performance'>('overview');

  // Mock data if none provided
  const mockData: AnalyticsData = {
    deliveries: {
      total: 1247,
      completed: 1156,
      pending: 67,
      cancelled: 24,
      growth: 12.5
    },
    revenue: {
      total: 89650,
      thisMonth: 23400,
      lastMonth: 20800,
      growth: 12.5
    },
    performance: {
      avgDeliveryTime: 2.4,
      onTimeRate: 94.2,
      customerSatisfaction: 4.7
    },
    trends: {
      daily: [
        { date: '2024-01-01', deliveries: 45, revenue: 3200 },
        { date: '2024-01-02', deliveries: 52, revenue: 3680 },
        { date: '2024-01-03', deliveries: 38, revenue: 2890 },
        { date: '2024-01-04', deliveries: 61, revenue: 4320 },
        { date: '2024-01-05', deliveries: 49, revenue: 3450 },
        { date: '2024-01-06', deliveries: 55, revenue: 3890 },
        { date: '2024-01-07', deliveries: 43, revenue: 3120 }
      ],
      monthly: [
        { month: 'Jan', deliveries: 1247, revenue: 89650 },
        { month: 'Feb', deliveries: 1156, revenue: 82340 },
        { month: 'Mar', deliveries: 1389, revenue: 95670 },
        { month: 'Apr', deliveries: 1298, revenue: 91230 },
        { month: 'May', deliveries: 1445, revenue: 98450 },
        { month: 'Jun', deliveries: 1523, revenue: 103890 }
      ]
    }
  };

  const analyticsData = data || mockData;

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change: number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, change, icon, color }) => (
    <motion.div
      whileHover={{ y: -2 }}
      className="bg-white p-6 rounded-xl shadow-lg border border-gray-200"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-800">{value}</p>
          <div className="flex items-center mt-2">
            {change >= 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            )}
            <span className={`text-sm font-medium ${change >= 0 ? 'text-green-500' : 'text-red-500'}`}>
              {Math.abs(change)}%
            </span>
            <span className="text-sm text-gray-500 ml-1">vs last period</span>
          </div>
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
    </motion.div>
  );

  const deliveryStatusData = [
    { name: 'Completed', value: analyticsData.deliveries.completed, color: '#10B981' },
    { name: 'Pending', value: analyticsData.deliveries.pending, color: '#F59E0B' },
    { name: 'Cancelled', value: analyticsData.deliveries.cancelled, color: '#EF4444' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Analytics Dashboard</h2>
          <p className="text-gray-600 mt-1">Track your delivery performance and insights</p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 lg:mt-0">
          {/* Time Range Selector */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            {['7d', '30d', '90d', '1y'].map((range) => (
              <button
                key={range}
                onClick={() => onTimeRangeChange?.(range as any)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  timeRange === range
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                {range === '7d' && 'Last 7 days'}
                {range === '30d' && 'Last 30 days'}
                {range === '90d' && 'Last 90 days'}
                {range === '1y' && 'Last year'}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Deliveries"
          value={analyticsData.deliveries.total.toLocaleString()}
          change={analyticsData.deliveries.growth}
          icon={<Package className="h-6 w-6 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Total Revenue"
          value={`₹${analyticsData.revenue.total.toLocaleString()}`}
          change={analyticsData.revenue.growth}
          icon={<DollarSign className="h-6 w-6 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title="Avg Delivery Time"
          value={`${analyticsData.performance.avgDeliveryTime}h`}
          change={-8.2}
          icon={<Clock className="h-6 w-6 text-white" />}
          color="bg-yellow-500"
        />
        <StatCard
          title="Customer Rating"
          value={`${analyticsData.performance.customerSatisfaction}/5`}
          change={5.1}
          icon={<Users className="h-6 w-6 text-white" />}
          color="bg-purple-500"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Delivery Trends */}
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Delivery Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analyticsData.trends.monthly}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="deliveries" 
                stroke="#3B82F6" 
                strokeWidth={2}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Revenue Trends */}
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Revenue Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={analyticsData.trends.monthly}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`₹${value}`, 'Revenue']} />
              <Bar dataKey="revenue" fill="#10B981" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Additional Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Delivery Status Distribution */}
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Delivery Status</h3>
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie
                data={deliveryStatusData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={100}
                paddingAngle={5}
                dataKey="value"
              >
                {deliveryStatusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
          <div className="mt-4 space-y-2">
            {deliveryStatusData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full" 
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-sm text-gray-600">{item.name}</span>
                </div>
                <span className="text-sm font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200 lg:col-span-2">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Performance Metrics</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">On-Time Delivery Rate</span>
                <span className="text-sm font-medium">{analyticsData.performance.onTimeRate}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${analyticsData.performance.onTimeRate}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Customer Satisfaction</span>
                <span className="text-sm font-medium">{analyticsData.performance.customerSatisfaction}/5.0</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${(analyticsData.performance.customerSatisfaction / 5) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Delivery Success Rate</span>
                <span className="text-sm font-medium">
                  {((analyticsData.deliveries.completed / analyticsData.deliveries.total) * 100).toFixed(1)}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${(analyticsData.deliveries.completed / analyticsData.deliveries.total) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
