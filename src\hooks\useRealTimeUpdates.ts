import { useEffect, useRef } from 'react';
import { useDeliveryStore } from '../store/deliveryStore';
import { useNotificationStore, createNotification } from '../store/notificationStore';
import { useAuthStore } from '../store/authStore';

interface RealTimeUpdateOptions {
  enablePackageUpdates?: boolean;
  enableNotifications?: boolean;
  updateInterval?: number;
}

export const useRealTimeUpdates = (options: RealTimeUpdateOptions = {}) => {
  const {
    enablePackageUpdates = true,
    enableNotifications = true,
    updateInterval = 30000 // 30 seconds
  } = options;

  const { user } = useAuthStore();
  const { getUserPackages, updatePackageStatus, addTimelineEvent } = useDeliveryStore();
  const { addNotification } = useNotificationStore();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Simulate real-time package updates
  const simulatePackageUpdates = () => {
    if (!user || !enablePackageUpdates) return;

    const userPackages = getUserPackages(user.id);
    const activePackages = userPackages.filter(pkg => 
      pkg.status === 'pending' || 
      pkg.status === 'confirmed' || 
      pkg.status === 'picked_up' || 
      pkg.status === 'in_transit'
    );

    activePackages.forEach(pkg => {
      // Simulate random status updates
      const random = Math.random();
      
      if (random < 0.1) { // 10% chance of status update
        let newStatus = pkg.status;
        let notificationTitle = '';
        let notificationMessage = '';

        switch (pkg.status) {
          case 'pending':
            newStatus = 'confirmed';
            notificationTitle = 'Package Confirmed';
            notificationMessage = `Your package ${pkg.trackingId} has been confirmed and a delivery partner has been assigned.`;
            break;
          case 'confirmed':
            newStatus = 'picked_up';
            notificationTitle = 'Package Picked Up';
            notificationMessage = `Your package ${pkg.trackingId} has been picked up and is on its way.`;
            break;
          case 'picked_up':
            newStatus = 'in_transit';
            notificationTitle = 'Package In Transit';
            notificationMessage = `Your package ${pkg.trackingId} is now in transit to the destination.`;
            break;
          case 'in_transit':
            if (random < 0.05) { // Lower chance for delivery
              newStatus = 'delivered';
              notificationTitle = 'Package Delivered';
              notificationMessage = `Your package ${pkg.trackingId} has been successfully delivered!`;
            }
            break;
        }

        if (newStatus !== pkg.status) {
          updatePackageStatus(pkg.trackingId, newStatus as any);
          
          // Add timeline event
          addTimelineEvent(pkg.trackingId, {
            status: newStatus,
            description: getStatusDescription(newStatus),
            location: getRandomLocation()
          });

          // Send notification
          if (enableNotifications) {
            addNotification(createNotification.info(
              notificationTitle,
              notificationMessage,
              `/track?id=${pkg.trackingId}`,
              'Track Package'
            ));
          }
        }
      }
    });
  };

  // Simulate location updates for in-transit packages
  const simulateLocationUpdates = () => {
    if (!user || !enablePackageUpdates) return;

    const userPackages = getUserPackages(user.id);
    const inTransitPackages = userPackages.filter(pkg => pkg.status === 'in_transit');

    inTransitPackages.forEach(pkg => {
      if (Math.random() < 0.2) { // 20% chance of location update
        addTimelineEvent(pkg.trackingId, {
          status: pkg.status,
          description: `Package location updated: ${getRandomLocation()}`,
          location: getRandomLocation()
        });
      }
    });
  };

  // Start real-time updates
  useEffect(() => {
    if (!user) return;

    const startUpdates = () => {
      intervalRef.current = setInterval(() => {
        simulatePackageUpdates();
        simulateLocationUpdates();
      }, updateInterval);
    };

    startUpdates();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [user, enablePackageUpdates, enableNotifications, updateInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    isActive: !!intervalRef.current,
    stop: () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    },
    start: () => {
      if (!intervalRef.current && user) {
        intervalRef.current = setInterval(() => {
          simulatePackageUpdates();
          simulateLocationUpdates();
        }, updateInterval);
      }
    }
  };
};

// Helper functions
function getStatusDescription(status: string): string {
  const descriptions = {
    pending: 'Package booking confirmed. Looking for delivery partner.',
    confirmed: 'Delivery partner assigned. Preparing for pickup.',
    picked_up: 'Package picked up from sender.',
    in_transit: 'Package is on the way to destination.',
    delivered: 'Package delivered successfully.',
    cancelled: 'Delivery cancelled.'
  };
  return descriptions[status as keyof typeof descriptions] || 'Status updated.';
}

function getRandomLocation(): string {
  const locations = [
    'Downtown Hub',
    'Central Sorting Facility',
    'North Distribution Center',
    'South Transit Point',
    'East Delivery Station',
    'West Processing Center',
    'Main Street Checkpoint',
    'City Center Hub',
    'Local Delivery Point',
    'Final Mile Station'
  ];
  return locations[Math.floor(Math.random() * locations.length)];
}

// WebSocket hook for real production use
export const useWebSocketUpdates = (url: string) => {
  const wsRef = useRef<WebSocket | null>(null);
  const { addNotification } = useNotificationStore();

  useEffect(() => {
    if (!url) return;

    const connectWebSocket = () => {
      wsRef.current = new WebSocket(url);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          
          // Handle different types of updates
          switch (data.type) {
            case 'package_update':
              // Handle package status updates
              break;
            case 'notification':
              addNotification(data.notification);
              break;
            default:
              console.log('Unknown message type:', data.type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000);
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [url]);

  return {
    isConnected: wsRef.current?.readyState === WebSocket.OPEN,
    send: (data: any) => {
      if (wsRef.current?.readyState === WebSocket.OPEN) {
        wsRef.current.send(JSON.stringify(data));
      }
    }
  };
};
