import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calculator, Package, Clock, Truck, MapPin, Info } from 'lucide-react';

interface CostCalculatorProps {
  onCostCalculated?: (cost: number, breakdown: CostBreakdown) => void;
  className?: string;
}

interface CostBreakdown {
  baseCost: number;
  distanceCost: number;
  sizeCost: number;
  weightCost: number;
  timeCost: number;
  total: number;
  estimatedTime: string;
}

interface CalculatorInputs {
  distance: number;
  packageSize: 'small' | 'medium' | 'large' | 'extra-large';
  packageWeight: number;
  deliveryTime: 'standard' | 'express' | 'same-day';
  packageType: 'documents' | 'electronics' | 'clothing' | 'food' | 'fragile' | 'other';
}

const CostCalculator: React.FC<CostCalculatorProps> = ({ onCostCalculated, className = '' }) => {
  const [inputs, setInputs] = useState<CalculatorInputs>({
    distance: 5,
    packageSize: 'medium',
    packageWeight: 1,
    deliveryTime: 'standard',
    packageType: 'other'
  });

  const [breakdown, setBreakdown] = useState<CostBreakdown | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);

  // Pricing configuration
  const pricing = {
    baseCost: 50,
    distanceRate: 8, // per km
    sizeMultipliers: {
      small: 1,
      medium: 1.3,
      large: 1.8,
      'extra-large': 2.5
    },
    weightRate: 15, // per kg above 1kg
    timeMultipliers: {
      standard: 1,
      express: 1.5,
      'same-day': 2
    },
    typeMultipliers: {
      documents: 0.8,
      electronics: 1.2,
      clothing: 1,
      food: 1.3,
      fragile: 1.5,
      other: 1
    }
  };

  const calculateCost = (): CostBreakdown => {
    const baseCost = pricing.baseCost;
    const distanceCost = inputs.distance * pricing.distanceRate;
    const sizeCost = baseCost * (pricing.sizeMultipliers[inputs.packageSize] - 1);
    const weightCost = Math.max(0, inputs.packageWeight - 1) * pricing.weightRate;
    const timeCost = baseCost * (pricing.timeMultipliers[inputs.deliveryTime] - 1);
    const typeCost = baseCost * (pricing.typeMultipliers[inputs.packageType] - 1);

    const subtotal = baseCost + distanceCost + sizeCost + weightCost + timeCost + typeCost;
    const total = Math.round(subtotal);

    const estimatedTimes = {
      standard: '4-6 hours',
      express: '2-3 hours',
      'same-day': '1-2 hours'
    };

    return {
      baseCost,
      distanceCost,
      sizeCost: sizeCost + typeCost,
      weightCost,
      timeCost,
      total,
      estimatedTime: estimatedTimes[inputs.deliveryTime]
    };
  };

  useEffect(() => {
    setIsCalculating(true);
    const timer = setTimeout(() => {
      const newBreakdown = calculateCost();
      setBreakdown(newBreakdown);
      onCostCalculated?.(newBreakdown.total, newBreakdown);
      setIsCalculating(false);
    }, 300);

    return () => clearTimeout(timer);
  }, [inputs]);

  const handleInputChange = (field: keyof CalculatorInputs, value: any) => {
    setInputs(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      <div className="flex items-center space-x-2 mb-6">
        <Calculator className="h-6 w-6 text-blue-600" />
        <h3 className="text-xl font-semibold text-gray-800">Cost Calculator</h3>
      </div>

      <div className="space-y-6">
        {/* Distance Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="h-4 w-4 inline mr-1" />
            Distance (km)
          </label>
          <input
            type="range"
            min="1"
            max="50"
            value={inputs.distance}
            onChange={(e) => handleInputChange('distance', parseInt(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-sm text-gray-500 mt-1">
            <span>1 km</span>
            <span className="font-medium text-blue-600">{inputs.distance} km</span>
            <span>50 km</span>
          </div>
        </div>

        {/* Package Size */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Package className="h-4 w-4 inline mr-1" />
            Package Size
          </label>
          <div className="grid grid-cols-2 gap-2">
            {Object.keys(pricing.sizeMultipliers).map((size) => (
              <button
                key={size}
                onClick={() => handleInputChange('packageSize', size)}
                className={`p-3 rounded-lg border-2 transition-all ${
                  inputs.packageSize === size
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-sm font-medium capitalize">{size.replace('-', ' ')}</div>
                <div className="text-xs text-gray-500">
                  {size === 'small' && '< 30cm'}
                  {size === 'medium' && '30-50cm'}
                  {size === 'large' && '50-80cm'}
                  {size === 'extra-large' && '> 80cm'}
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Package Weight */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Weight (kg)
          </label>
          <input
            type="number"
            min="0.1"
            max="50"
            step="0.1"
            value={inputs.packageWeight}
            onChange={(e) => handleInputChange('packageWeight', parseFloat(e.target.value))}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        {/* Delivery Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Clock className="h-4 w-4 inline mr-1" />
            Delivery Speed
          </label>
          <div className="space-y-2">
            {Object.entries(pricing.timeMultipliers).map(([time, multiplier]) => (
              <button
                key={time}
                onClick={() => handleInputChange('deliveryTime', time)}
                className={`w-full p-3 rounded-lg border-2 transition-all text-left ${
                  inputs.deliveryTime === time
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium capitalize">{time.replace('-', ' ')}</div>
                    <div className="text-sm text-gray-500">
                      {time === 'standard' && '4-6 hours delivery'}
                      {time === 'express' && '2-3 hours delivery'}
                      {time === 'same-day' && '1-2 hours delivery'}
                    </div>
                  </div>
                  <div className="text-sm text-blue-600 font-medium">
                    +{Math.round((multiplier - 1) * 100)}%
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Package Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Package Type
          </label>
          <select
            value={inputs.packageType}
            onChange={(e) => handleInputChange('packageType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {Object.keys(pricing.typeMultipliers).map((type) => (
              <option key={type} value={type}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* Cost Breakdown */}
        {breakdown && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-50 rounded-lg p-4 space-y-3"
          >
            <h4 className="font-semibold text-gray-800 flex items-center">
              <Info className="h-4 w-4 mr-2" />
              Cost Breakdown
            </h4>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Base cost:</span>
                <span>₹{breakdown.baseCost}</span>
              </div>
              <div className="flex justify-between">
                <span>Distance ({inputs.distance} km):</span>
                <span>₹{Math.round(breakdown.distanceCost)}</span>
              </div>
              {breakdown.sizeCost > 0 && (
                <div className="flex justify-between">
                  <span>Size & type:</span>
                  <span>₹{Math.round(breakdown.sizeCost)}</span>
                </div>
              )}
              {breakdown.weightCost > 0 && (
                <div className="flex justify-between">
                  <span>Extra weight:</span>
                  <span>₹{Math.round(breakdown.weightCost)}</span>
                </div>
              )}
              {breakdown.timeCost > 0 && (
                <div className="flex justify-between">
                  <span>Express delivery:</span>
                  <span>₹{Math.round(breakdown.timeCost)}</span>
                </div>
              )}
            </div>
            
            <div className="border-t border-gray-200 pt-2">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-lg">Total:</span>
                <span className="font-bold text-xl text-blue-600">₹{breakdown.total}</span>
              </div>
              <div className="text-sm text-gray-500 mt-1">
                <Clock className="h-3 w-3 inline mr-1" />
                Estimated delivery: {breakdown.estimatedTime}
              </div>
            </div>
          </motion.div>
        )}

        {isCalculating && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Calculating...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CostCalculator;
