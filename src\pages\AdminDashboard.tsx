import React, { useState } from 'react';
import { 
  Package, Users, Truck, DollarSign, TrendingUp, Eye, CheckCircle, XCircle, 
  Clock, Star, BarChart3, Activity, AlertTriangle, Filter, Download,
  Calendar, MapPin, Phone, Mail, Search, Plus, Settings, Bell
} from 'lucide-react';

const AdminDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');

  // Enhanced mock data with more realistic statistics
  const stats = [
    {
      title: 'Total Deliveries',
      value: '12,847',
      change: '+12.5%',
      icon: Package,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      trend: 'up',
      subtitle: 'This month'
    },
    {
      title: 'Active Partners',
      value: '156',
      change: '****%',
      icon: Truck,
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      trend: 'up',
      subtitle: 'Online now'
    },
    {
      title: 'Total Users',
      value: '8,924',
      change: '+23.1%',
      icon: Users,
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      trend: 'up',
      subtitle: 'Registered'
    },
    {
      title: 'Revenue',
      value: '₹4,67,890',
      change: '+18.7%',
      icon: DollarSign,
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      trend: 'up',
      subtitle: 'This month'
    }
  ];

  const recentDeliveries = [
    {
      id: 'QD12345678',
      customer: 'Rajesh Kumar',
      partner: 'Amit Singh',
      status: 'Delivered',
      time: '2 hours ago',
      amount: '₹145',
      priority: 'normal',
      distance: '3.2 km'
    },
    {
      id: 'QD87654321',
      customer: 'Priya Sharma',
      partner: 'Vikash Yadav',
      status: 'In Transit',
      time: '45 minutes ago',
      amount: '₹89',
      priority: 'urgent',
      distance: '1.8 km'
    },
    {
      id: 'QD13579246',
      customer: 'Arjun Patel',
      partner: 'Ravi Kumar',
      status: 'Picked Up',
      time: '1 hour ago',
      amount: '₹234',
      priority: 'normal',
      distance: '5.1 km'
    },
    {
      id: 'QD24681357',
      customer: 'Sneha Gupta',
      partner: 'Manoj Singh',
      status: 'Pending',
      time: '15 minutes ago',
      amount: '₹67',
      priority: 'normal',
      distance: '2.3 km'
    }
  ];

  const partnerApplications = [
    {
      id: 1,
      name: 'Rohit Verma',
      email: '<EMAIL>',
      phone: '+91 9876543210',
      vehicle: 'Motorcycle',
      status: 'Pending',
      appliedDate: '2024-01-16',
      experience: '2 years',
      location: 'Mumbai Central'
    },
    {
      id: 2,
      name: 'Kavita Sharma',
      email: '<EMAIL>',
      phone: '+91 8765432109',
      vehicle: 'Scooter',
      status: 'Under Review',
      appliedDate: '2024-01-15',
      experience: '1 year',
      location: 'Andheri West'
    },
    {
      id: 3,
      name: 'Deepak Joshi',
      email: '<EMAIL>',
      phone: '+91 7654321098',
      vehicle: 'Bicycle',
      status: 'Approved',
      appliedDate: '2024-01-14',
      experience: '6 months',
      location: 'Bandra East'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'In Transit':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Picked Up':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Pending':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'Under Review':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'Approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'Rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600 mt-1">Monitor and manage your delivery operations</p>
            </div>
            <div className="flex items-center space-x-4">
              <select 
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
              <button className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className={`${stat.color} p-3 rounded-xl shadow-lg`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-right">
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-green-600 text-sm font-semibold">{stat.change}</span>
                  </div>
                  <span className="text-xs text-gray-500">{stat.subtitle}</span>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Enhanced Tabs */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: BarChart3 },
                { id: 'deliveries', label: 'Deliveries', icon: Package },
                { id: 'partners', label: 'Partner Applications', icon: Truck },
                { id: 'users', label: 'Users', icon: Users },
                { id: 'analytics', label: 'Analytics', icon: Activity }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 border-b-2 font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-5 w-5" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* Enhanced Overview Tab */}
            {activeTab === 'overview' && (
              <div className="space-y-8">
                <div className="grid lg:grid-cols-3 gap-6">
                  {/* Recent Activity */}
                  <div className="lg:col-span-2">
                    <div className="flex items-center justify-between mb-6">
                      <h3 className="text-xl font-semibold text-gray-900">Recent Deliveries</h3>
                      <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                        View All
                      </button>
                    </div>
                    <div className="space-y-4">
                      {recentDeliveries.slice(0, 4).map((delivery) => (
                        <div key={delivery.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                          <div className="flex items-center space-x-4">
                            <div className="bg-blue-100 p-2 rounded-lg">
                              <Package className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">#{delivery.id}</p>
                              <p className="text-sm text-gray-600">{delivery.customer} → {delivery.partner}</p>
                              <p className="text-xs text-gray-500">{delivery.distance} • {delivery.time}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(delivery.status)}`}>
                                {delivery.status}
                              </span>
                              {delivery.priority === 'urgent' && (
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(delivery.priority)}`}>
                                  Urgent
                                </span>
                              )}
                            </div>
                            <p className="font-semibold text-gray-900">{delivery.amount}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h3>
                    <div className="space-y-3">
                      <button className="w-full p-4 bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-200 text-left group">
                        <div className="flex items-center space-x-3">
                          <Package className="h-6 w-6 group-hover:scale-110 transition-transform" />
                          <div>
                            <div className="font-semibold">Manage Orders</div>
                            <div className="text-xs text-blue-600">View and track all deliveries</div>
                          </div>
                        </div>
                      </button>
                      <button className="w-full p-4 bg-gradient-to-r from-green-50 to-green-100 text-green-700 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-200 text-left group">
                        <div className="flex items-center space-x-3">
                          <Truck className="h-6 w-6 group-hover:scale-110 transition-transform" />
                          <div>
                            <div className="font-semibold">Partner Requests</div>
                            <div className="text-xs text-green-600">Review new applications</div>
                          </div>
                        </div>
                      </button>
                      <button className="w-full p-4 bg-gradient-to-r from-purple-50 to-purple-100 text-purple-700 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-200 text-left group">
                        <div className="flex items-center space-x-3">
                          <Users className="h-6 w-6 group-hover:scale-110 transition-transform" />
                          <div>
                            <div className="font-semibold">User Management</div>
                            <div className="text-xs text-purple-600">Manage customer accounts</div>
                          </div>
                        </div>
                      </button>
                      <button className="w-full p-4 bg-gradient-to-r from-orange-50 to-orange-100 text-orange-700 rounded-xl hover:from-orange-100 hover:to-orange-200 transition-all duration-200 text-left group">
                        <div className="flex items-center space-x-3">
                          <BarChart3 className="h-6 w-6 group-hover:scale-110 transition-transform" />
                          <div>
                            <div className="font-semibold">Analytics</div>
                            <div className="text-xs text-orange-600">View detailed reports</div>
                          </div>
                        </div>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="grid md:grid-cols-3 gap-6">
                  <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-blue-900">Delivery Success Rate</h4>
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-blue-900 mb-2">98.7%</div>
                    <div className="text-sm text-blue-700">+2.1% from last month</div>
                  </div>
                  <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-green-900">Avg. Delivery Time</h4>
                      <Clock className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="text-3xl font-bold text-green-900 mb-2">24 min</div>
                    <div className="text-sm text-green-700">-3 min from last month</div>
                  </div>
                  <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-purple-900">Customer Rating</h4>
                      <Star className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="text-3xl font-bold text-purple-900 mb-2">4.8/5</div>
                    <div className="text-sm text-purple-700">+0.2 from last month</div>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Deliveries Tab */}
            {activeTab === 'deliveries' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-xl font-semibold text-gray-900">All Deliveries</h3>
                  <div className="flex flex-wrap items-center gap-3">
                    <div className="flex items-center space-x-2">
                      <Filter className="h-4 w-4 text-gray-500" />
                      <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>All Status</option>
                        <option>Pending</option>
                        <option>In Transit</option>
                        <option>Delivered</option>
                      </select>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Search className="h-4 w-4 text-gray-500" />
                      <input
                        type="text"
                        placeholder="Search orders..."
                        className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center space-x-2">
                      <Download className="h-4 w-4" />
                      <span>Export</span>
                    </button>
                  </div>
                </div>
                
                <div className="bg-white border border-gray-200 rounded-xl overflow-hidden">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead className="bg-gray-50 border-b border-gray-200">
                        <tr>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Order ID</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Customer</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Partner</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Status</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Amount</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Distance</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Time</th>
                          <th className="text-left py-4 px-6 font-semibold text-gray-700">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-200">
                        {recentDeliveries.map((delivery) => (
                          <tr key={delivery.id} className="hover:bg-gray-50 transition-colors">
                            <td className="py-4 px-6 font-mono text-sm font-medium text-gray-900">{delivery.id}</td>
                            <td className="py-4 px-6 text-gray-900">{delivery.customer}</td>
                            <td className="py-4 px-6 text-gray-900">{delivery.partner}</td>
                            <td className="py-4 px-6">
                              <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(delivery.status)}`}>
                                {delivery.status}
                              </span>
                            </td>
                            <td className="py-4 px-6 font-semibold text-gray-900">{delivery.amount}</td>
                            <td className="py-4 px-6 text-gray-600">{delivery.distance}</td>
                            <td className="py-4 px-6 text-sm text-gray-600">{delivery.time}</td>
                            <td className="py-4 px-6">
                              <button className="text-blue-600 hover:text-blue-700 p-1 rounded transition-colors">
                                <Eye className="h-4 w-4" />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {/* Enhanced Partner Applications Tab */}
            {activeTab === 'partners' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-xl font-semibold text-gray-900">Partner Applications</h3>
                  <div className="flex items-center space-x-3">
                    <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option>All Applications</option>
                      <option>Pending</option>
                      <option>Under Review</option>
                      <option>Approved</option>
                      <option>Rejected</option>
                    </select>
                    <button className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors flex items-center space-x-2">
                      <Plus className="h-4 w-4" />
                      <span>Invite Partner</span>
                    </button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  {partnerApplications.map((application) => (
                    <div key={application.id} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-200">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-4 mb-4">
                            <div className="bg-blue-100 p-3 rounded-full">
                              <Truck className="h-6 w-6 text-blue-600" />
                            </div>
                            <div>
                              <h4 className="text-lg font-semibold text-gray-900">{application.name}</h4>
                              <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(application.status)}`}>
                                {application.status}
                              </span>
                            </div>
                          </div>
                          
                          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">{application.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Phone className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">{application.phone}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Truck className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">{application.vehicle}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">{application.location}</span>
                            </div>
                          </div>
                          
                          <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500">
                            <span>Experience: {application.experience}</span>
                            <span>•</span>
                            <span>Applied: {application.appliedDate}</span>
                          </div>
                        </div>
                        
                        <div className="mt-6 lg:mt-0 lg:ml-6 flex flex-wrap gap-2">
                          <button className="bg-green-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-700 transition-colors flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4" />
                            <span>Approve</span>
                          </button>
                          <button className="bg-red-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-red-700 transition-colors flex items-center space-x-2">
                            <XCircle className="h-4 w-4" />
                            <span>Reject</span>
                          </button>
                          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center space-x-2">
                            <Eye className="h-4 w-4" />
                            <span>View Details</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Enhanced Users Tab */}
            {activeTab === 'users' && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <h3 className="text-xl font-semibold text-gray-900">User Management</h3>
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search users..."
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                      Search
                    </button>
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-8 text-center">
                  <div className="bg-blue-100 p-4 rounded-full w-16 h-16 mx-auto mb-4">
                    <Users className="h-8 w-8 text-blue-600 mx-auto" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-3">Comprehensive User Management</h4>
                  <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                    Advanced user management system with detailed profiles, order history tracking, 
                    account status monitoring, customer support integration, and analytics dashboard.
                  </p>
                  <div className="grid md:grid-cols-3 gap-4 max-w-3xl mx-auto">
                    <div className="bg-white p-4 rounded-lg">
                      <h5 className="font-semibold text-gray-900 mb-2">User Profiles</h5>
                      <p className="text-sm text-gray-600">Detailed customer information and preferences</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg">
                      <h5 className="font-semibold text-gray-900 mb-2">Order History</h5>
                      <p className="text-sm text-gray-600">Complete delivery and transaction records</p>
                    </div>
                    <div className="bg-white p-4 rounded-lg">
                      <h5 className="font-semibold text-gray-900 mb-2">Support Tools</h5>
                      <p className="text-sm text-gray-600">Integrated customer service features</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* New Analytics Tab */}
            {activeTab === 'analytics' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900">Analytics & Reports</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors flex items-center space-x-2">
                    <Download className="h-4 w-4" />
                    <span>Export Report</span>
                  </button>
                </div>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold text-green-900">Revenue Analytics</h4>
                      <TrendingUp className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="text-3xl font-bold text-green-900 mb-2">₹4,67,890</div>
                    <div className="text-sm text-green-700 mb-4">+18.7% from last month</div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-green-800">Daily Average:</span>
                        <span className="font-semibold text-green-900">₹15,596</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-green-800">Peak Day:</span>
                        <span className="font-semibold text-green-900">₹23,450</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-lg font-semibold text-purple-900">Performance Metrics</h4>
                      <Activity className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-purple-800">Delivery Success Rate</span>
                          <span className="font-semibold text-purple-900">98.7%</span>
                        </div>
                        <div className="w-full bg-purple-200 rounded-full h-2">
                          <div className="bg-purple-600 h-2 rounded-full" style={{width: '98.7%'}}></div>
                        </div>
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-purple-800">Customer Satisfaction</span>
                          <span className="font-semibold text-purple-900">4.8/5</span>
                        </div>
                        <div className="w-full bg-purple-200 rounded-full h-2">
                          <div className="bg-purple-600 h-2 rounded-full" style={{width: '96%'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-xl p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Delivery Trends</h4>
                  <div className="grid md:grid-cols-4 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">156</div>
                      <div className="text-sm text-blue-800">Today's Orders</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">89</div>
                      <div className="text-sm text-green-800">Active Partners</div>
                    </div>
                    <div className="text-center p-4 bg-orange-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">24 min</div>
                      <div className="text-sm text-orange-800">Avg. Delivery Time</div>
                    </div>
                    <div className="text-center p-4 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">12</div>
                      <div className="text-sm text-purple-800">Pending Reviews</div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;