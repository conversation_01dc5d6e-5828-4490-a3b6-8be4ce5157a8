import { useState, useEffect } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface DarkModeConfig {
  defaultTheme?: Theme;
  localStorageKey?: string;
  classNameDark?: string;
  classNameLight?: string;
}

export const useDarkMode = (config: DarkModeConfig = {}) => {
  const {
    defaultTheme = 'system',
    localStorageKey = 'quickdrop-theme',
    classNameDark = 'dark',
    classNameLight = 'light'
  } = config;

  // Get initial theme from localStorage or use default
  const getInitialTheme = (): Theme => {
    if (typeof window === 'undefined') return defaultTheme;
    
    const savedTheme = localStorage.getItem(localStorageKey) as Theme;
    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
      return savedTheme;
    }
    
    return defaultTheme;
  };

  const [theme, setTheme] = useState<Theme>(getInitialTheme);
  const [isDark, setIsDark] = useState(false);

  // Check if system prefers dark mode
  const getSystemTheme = (): boolean => {
    if (typeof window === 'undefined') return false;
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  };

  // Apply theme to document
  const applyTheme = (currentTheme: Theme) => {
    if (typeof window === 'undefined') return;

    const root = document.documentElement;
    const body = document.body;

    // Remove existing theme classes
    root.classList.remove(classNameDark, classNameLight);
    body.classList.remove(classNameDark, classNameLight);

    let shouldBeDark = false;

    if (currentTheme === 'dark') {
      shouldBeDark = true;
    } else if (currentTheme === 'light') {
      shouldBeDark = false;
    } else {
      // system theme
      shouldBeDark = getSystemTheme();
    }

    // Apply appropriate classes
    const themeClass = shouldBeDark ? classNameDark : classNameLight;
    root.classList.add(themeClass);
    body.classList.add(themeClass);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', shouldBeDark ? '#1f2937' : '#ffffff');
    }

    setIsDark(shouldBeDark);
  };

  // Change theme
  const changeTheme = (newTheme: Theme) => {
    setTheme(newTheme);
    localStorage.setItem(localStorageKey, newTheme);
    applyTheme(newTheme);
  };

  // Toggle between light and dark (skips system)
  const toggleTheme = () => {
    const newTheme = isDark ? 'light' : 'dark';
    changeTheme(newTheme);
  };

  // Cycle through all themes: light -> dark -> system
  const cycleTheme = () => {
    const themeOrder: Theme[] = ['light', 'dark', 'system'];
    const currentIndex = themeOrder.indexOf(theme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    changeTheme(themeOrder[nextIndex]);
  };

  // Listen for system theme changes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleSystemThemeChange = () => {
      if (theme === 'system') {
        applyTheme('system');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme]);

  // Apply theme on mount and theme change
  useEffect(() => {
    applyTheme(theme);
  }, [theme]);

  // Initialize theme on mount
  useEffect(() => {
    const initialTheme = getInitialTheme();
    setTheme(initialTheme);
    applyTheme(initialTheme);
  }, []);

  return {
    theme,
    isDark,
    isLight: !isDark,
    isSystem: theme === 'system',
    setTheme: changeTheme,
    toggleTheme,
    cycleTheme,
    systemTheme: getSystemTheme() ? 'dark' : 'light'
  };
};

// Theme context for app-wide theme management
import React, { createContext, useContext } from 'react';

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  isLight: boolean;
  isSystem: boolean;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  cycleTheme: () => void;
  systemTheme: 'light' | 'dark';
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const themeState = useDarkMode();

  return (
    <ThemeContext.Provider value={themeState}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Theme toggle component
export const ThemeToggle: React.FC<{ 
  className?: string;
  showLabel?: boolean;
  variant?: 'button' | 'switch' | 'dropdown';
}> = ({ 
  className = '', 
  showLabel = false,
  variant = 'button'
}) => {
  const { theme, isDark, cycleTheme } = useTheme();

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return '☀️';
      case 'dark':
        return '🌙';
      case 'system':
        return '💻';
      default:
        return '☀️';
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'Light';
    }
  };

  if (variant === 'switch') {
    return (
      <label className={`relative inline-flex items-center cursor-pointer ${className}`}>
        <input
          type="checkbox"
          checked={isDark}
          onChange={cycleTheme}
          className="sr-only"
        />
        <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600`}></div>
        {showLabel && (
          <span className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-300">
            {getThemeLabel()}
          </span>
        )}
      </label>
    );
  }

  if (variant === 'dropdown') {
    return (
      <select
        value={theme}
        onChange={(e) => cycleTheme()}
        className={`bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ${className}`}
      >
        <option value="light">☀️ Light</option>
        <option value="dark">🌙 Dark</option>
        <option value="system">💻 System</option>
      </select>
    );
  }

  // Default button variant
  return (
    <button
      onClick={cycleTheme}
      className={`p-2 rounded-lg bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors ${className}`}
      title={`Current theme: ${getThemeLabel()}. Click to cycle themes.`}
    >
      <span className="text-lg">{getThemeIcon()}</span>
      {showLabel && (
        <span className="ml-2 text-sm">{getThemeLabel()}</span>
      )}
    </button>
  );
};

export default useDarkMode;
