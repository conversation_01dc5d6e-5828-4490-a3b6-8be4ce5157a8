import { ValidationError } from '../components/ErrorBoundary';

// Input sanitization functions
export const sanitizeInput = {
  // Remove HTML tags and dangerous characters
  html: (input: string): string => {
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<[^>]*>/g, '')
      .trim();
  },

  // Sanitize for SQL injection prevention (basic)
  sql: (input: string): string => {
    return input
      .replace(/['";\\]/g, '')
      .replace(/--/g, '')
      .replace(/\/\*/g, '')
      .replace(/\*\//g, '')
      .trim();
  },

  // Sanitize phone numbers
  phone: (input: string): string => {
    return input.replace(/[^\d+\-\s()]/g, '').trim();
  },

  // Sanitize email
  email: (input: string): string => {
    return input.toLowerCase().trim();
  },

  // General text sanitization
  text: (input: string): string => {
    return input
      .replace(/[<>]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+=/gi, '')
      .trim();
  },

  // Sanitize numbers
  number: (input: string): string => {
    return input.replace(/[^\d.-]/g, '');
  },

  // Sanitize addresses
  address: (input: string): string => {
    return input
      .replace(/[<>]/g, '')
      .replace(/[^\w\s,.-]/g, '')
      .trim();
  }
};

// Validation rules
export const validators = {
  required: (value: any, fieldName: string = 'Field') => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      throw new ValidationError(`${fieldName} is required`);
    }
    return true;
  },

  email: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new ValidationError('Please enter a valid email address', 'email');
    }
    return true;
  },

  phone: (phone: string) => {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
    if (!phoneRegex.test(cleanPhone)) {
      throw new ValidationError('Please enter a valid phone number', 'phone');
    }
    return true;
  },

  password: (password: string) => {
    if (password.length < 8) {
      throw new ValidationError('Password must be at least 8 characters long', 'password');
    }
    if (!/(?=.*[a-z])/.test(password)) {
      throw new ValidationError('Password must contain at least one lowercase letter', 'password');
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      throw new ValidationError('Password must contain at least one uppercase letter', 'password');
    }
    if (!/(?=.*\d)/.test(password)) {
      throw new ValidationError('Password must contain at least one number', 'password');
    }
    return true;
  },

  minLength: (value: string, min: number, fieldName: string = 'Field') => {
    if (value.length < min) {
      throw new ValidationError(`${fieldName} must be at least ${min} characters long`);
    }
    return true;
  },

  maxLength: (value: string, max: number, fieldName: string = 'Field') => {
    if (value.length > max) {
      throw new ValidationError(`${fieldName} must not exceed ${max} characters`);
    }
    return true;
  },

  numeric: (value: string, fieldName: string = 'Field') => {
    if (!/^\d+(\.\d+)?$/.test(value)) {
      throw new ValidationError(`${fieldName} must be a valid number`);
    }
    return true;
  },

  positiveNumber: (value: number, fieldName: string = 'Field') => {
    if (value <= 0) {
      throw new ValidationError(`${fieldName} must be a positive number`);
    }
    return true;
  },

  range: (value: number, min: number, max: number, fieldName: string = 'Field') => {
    if (value < min || value > max) {
      throw new ValidationError(`${fieldName} must be between ${min} and ${max}`);
    }
    return true;
  },

  trackingId: (trackingId: string) => {
    const trackingRegex = /^QD\d{8}$/;
    if (!trackingRegex.test(trackingId)) {
      throw new ValidationError('Invalid tracking ID format', 'trackingId');
    }
    return true;
  },

  address: (address: string) => {
    if (address.length < 10) {
      throw new ValidationError('Address must be at least 10 characters long', 'address');
    }
    if (address.length > 200) {
      throw new ValidationError('Address must not exceed 200 characters', 'address');
    }
    return true;
  },

  packageWeight: (weight: number) => {
    if (weight <= 0) {
      throw new ValidationError('Package weight must be greater than 0', 'weight');
    }
    if (weight > 50) {
      throw new ValidationError('Package weight cannot exceed 50kg', 'weight');
    }
    return true;
  }
};

// Form validation schemas
export interface ValidationSchema {
  [key: string]: Array<(value: any) => boolean>;
}

export const validationSchemas = {
  login: {
    email: [
      (value: string) => validators.required(value, 'Email'),
      (value: string) => validators.email(sanitizeInput.email(value))
    ],
    password: [
      (value: string) => validators.required(value, 'Password'),
      (value: string) => validators.minLength(value, 6, 'Password')
    ]
  },

  register: {
    name: [
      (value: string) => validators.required(value, 'Name'),
      (value: string) => validators.minLength(value, 2, 'Name'),
      (value: string) => validators.maxLength(value, 50, 'Name')
    ],
    email: [
      (value: string) => validators.required(value, 'Email'),
      (value: string) => validators.email(sanitizeInput.email(value))
    ],
    password: [
      (value: string) => validators.required(value, 'Password'),
      (value: string) => validators.password(value)
    ],
    phone: [
      (value: string) => validators.required(value, 'Phone'),
      (value: string) => validators.phone(sanitizeInput.phone(value))
    ]
  },

  packageBooking: {
    pickupAddress: [
      (value: string) => validators.required(value, 'Pickup address'),
      (value: string) => validators.address(sanitizeInput.address(value))
    ],
    deliveryAddress: [
      (value: string) => validators.required(value, 'Delivery address'),
      (value: string) => validators.address(sanitizeInput.address(value))
    ],
    pickupContact: [
      (value: string) => validators.required(value, 'Pickup contact'),
      (value: string) => validators.phone(sanitizeInput.phone(value))
    ],
    deliveryContact: [
      (value: string) => validators.required(value, 'Delivery contact'),
      (value: string) => validators.phone(sanitizeInput.phone(value))
    ],
    packageWeight: [
      (value: string) => validators.required(value, 'Package weight'),
      (value: string) => validators.numeric(value, 'Package weight'),
      (value: string) => validators.packageWeight(parseFloat(value))
    ]
  },

  tracking: {
    trackingId: [
      (value: string) => validators.required(value, 'Tracking ID'),
      (value: string) => validators.trackingId(sanitizeInput.text(value))
    ]
  }
};

// Main validation function
export const validateForm = (data: Record<string, any>, schema: ValidationSchema): Record<string, string> => {
  const errors: Record<string, string> = {};

  Object.keys(schema).forEach(field => {
    const value = data[field];
    const fieldValidators = schema[field];

    try {
      fieldValidators.forEach(validator => validator(value));
    } catch (error) {
      if (error instanceof ValidationError) {
        errors[field] = error.message;
      } else {
        errors[field] = 'Validation error occurred';
      }
    }
  });

  return errors;
};

// Real-time validation hook
export const useFormValidation = (schema: ValidationSchema) => {
  const validateField = (field: string, value: any): string | null => {
    const fieldValidators = schema[field];
    if (!fieldValidators) return null;

    try {
      fieldValidators.forEach(validator => validator(value));
      return null;
    } catch (error) {
      if (error instanceof ValidationError) {
        return error.message;
      }
      return 'Validation error occurred';
    }
  };

  const validateAll = (data: Record<string, any>): Record<string, string> => {
    return validateForm(data, schema);
  };

  return { validateField, validateAll };
};

// Security utilities
export const security = {
  // Generate CSRF token
  generateCSRFToken: (): string => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  },

  // Rate limiting helper
  createRateLimiter: (maxRequests: number, windowMs: number) => {
    const requests = new Map<string, number[]>();

    return (identifier: string): boolean => {
      const now = Date.now();
      const userRequests = requests.get(identifier) || [];
      
      // Remove old requests outside the window
      const validRequests = userRequests.filter(time => now - time < windowMs);
      
      if (validRequests.length >= maxRequests) {
        return false; // Rate limit exceeded
      }
      
      validRequests.push(now);
      requests.set(identifier, validRequests);
      return true; // Request allowed
    };
  },

  // Input length validation
  validateInputLength: (input: string, maxLength: number = 1000): boolean => {
    return input.length <= maxLength;
  },

  // Check for suspicious patterns
  detectSuspiciousInput: (input: string): boolean => {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+=/i,
      /eval\(/i,
      /expression\(/i,
      /vbscript:/i,
      /data:text\/html/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(input));
  }
};
