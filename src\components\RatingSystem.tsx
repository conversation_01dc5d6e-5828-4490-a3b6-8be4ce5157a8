import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, ThumbsUp, ThumbsDown, MessageSquare, User, Calendar } from 'lucide-react';

interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  comment: string;
  date: string;
  deliveryId: string;
  isVerified: boolean;
  helpful: number;
  notHelpful: number;
  userVote?: 'helpful' | 'not-helpful';
}

interface RatingBreakdown {
  average: number;
  total: number;
  distribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

interface RatingSystemProps {
  targetId: string; // Partner ID or Service ID
  targetType: 'partner' | 'service';
  reviews?: Review[];
  ratingBreakdown?: RatingBreakdown;
  canReview?: boolean;
  onSubmitReview?: (rating: number, comment: string) => void;
  className?: string;
}

const RatingSystem: React.FC<RatingSystemProps> = ({
  targetId,
  targetType,
  reviews = [],
  ratingBreakdown,
  canReview = false,
  onSubmitReview,
  className = ''
}) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newRating, setNewRating] = useState(0);
  const [newComment, setNewComment] = useState('');
  const [hoveredStar, setHoveredStar] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock data if not provided
  const mockRatingBreakdown: RatingBreakdown = ratingBreakdown || {
    average: 4.6,
    total: 127,
    distribution: {
      5: 89,
      4: 23,
      3: 8,
      2: 4,
      1: 3
    }
  };

  const mockReviews: Review[] = reviews.length > 0 ? reviews : [
    {
      id: '1',
      userId: 'user1',
      userName: 'Sarah Johnson',
      rating: 5,
      comment: 'Excellent service! The delivery was on time and the driver was very professional. Highly recommended!',
      date: '2024-01-15',
      deliveryId: 'del-001',
      isVerified: true,
      helpful: 12,
      notHelpful: 1,
    },
    {
      id: '2',
      userId: 'user2',
      userName: 'Mike Chen',
      rating: 4,
      comment: 'Good service overall. Package arrived safely and on time. Only minor issue was communication could be better.',
      date: '2024-01-12',
      deliveryId: 'del-002',
      isVerified: true,
      helpful: 8,
      notHelpful: 0,
    },
    {
      id: '3',
      userId: 'user3',
      userName: 'Emily Davis',
      rating: 5,
      comment: 'Amazing experience! Very careful with fragile items and great communication throughout the delivery process.',
      date: '2024-01-10',
      deliveryId: 'del-003',
      isVerified: true,
      helpful: 15,
      notHelpful: 2,
    }
  ];

  const handleSubmitReview = async () => {
    if (newRating === 0 || !newComment.trim()) return;

    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API call
      onSubmitReview?.(newRating, newComment.trim());
      setShowReviewForm(false);
      setNewRating(0);
      setNewComment('');
    } catch (error) {
      console.error('Failed to submit review:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md', interactive = false) => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    };

    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= (interactive ? (hoveredStar || newRating) : rating)
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300'
            } ${interactive ? 'cursor-pointer hover:scale-110 transition-transform' : ''}`}
            onClick={interactive ? () => setNewRating(star) : undefined}
            onMouseEnter={interactive ? () => setHoveredStar(star) : undefined}
            onMouseLeave={interactive ? () => setHoveredStar(0) : undefined}
          />
        ))}
      </div>
    );
  };

  const getPercentage = (count: number) => {
    return mockRatingBreakdown.total > 0 ? (count / mockRatingBreakdown.total) * 100 : 0;
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-800">
          {targetType === 'partner' ? 'Partner Reviews' : 'Service Reviews'}
        </h3>
        {canReview && (
          <button
            onClick={() => setShowReviewForm(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
          >
            Write Review
          </button>
        )}
      </div>

      {/* Rating Overview */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {/* Average Rating */}
        <div className="text-center">
          <div className="text-4xl font-bold text-gray-800 mb-2">
            {mockRatingBreakdown.average.toFixed(1)}
          </div>
          {renderStars(mockRatingBreakdown.average, 'lg')}
          <p className="text-gray-600 mt-2">
            Based on {mockRatingBreakdown.total} reviews
          </p>
        </div>

        {/* Rating Distribution */}
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center space-x-3">
              <span className="text-sm text-gray-600 w-8">{rating}</span>
              <Star className="h-4 w-4 text-yellow-400 fill-current" />
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getPercentage(mockRatingBreakdown.distribution[rating as keyof typeof mockRatingBreakdown.distribution])}%` }}
                ></div>
              </div>
              <span className="text-sm text-gray-600 w-8">
                {mockRatingBreakdown.distribution[rating as keyof typeof mockRatingBreakdown.distribution]}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Review Form */}
      <AnimatePresence>
        {showReviewForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-50 rounded-lg p-6 mb-6"
          >
            <h4 className="font-semibold text-gray-800 mb-4">Write a Review</h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Rating
                </label>
                {renderStars(newRating, 'lg', true)}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Review
                </label>
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Share your experience..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                />
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={handleSubmitReview}
                  disabled={newRating === 0 || !newComment.trim() || isSubmitting}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Review'}
                </button>
                <button
                  onClick={() => {
                    setShowReviewForm(false);
                    setNewRating(0);
                    setNewComment('');
                  }}
                  className="bg-gray-200 text-gray-800 px-6 py-2 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                >
                  Cancel
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Reviews List */}
      <div className="space-y-6">
        <h4 className="font-semibold text-gray-800">Recent Reviews</h4>
        
        {mockReviews.map((review) => (
          <motion.div
            key={review.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="border border-gray-200 rounded-lg p-4"
          >
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                  {review.userName.charAt(0)}
                </div>
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h5 className="font-medium text-gray-800">{review.userName}</h5>
                  {review.isVerified && (
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                      Verified
                    </span>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  {renderStars(review.rating, 'sm')}
                  <span className="text-sm text-gray-500">
                    <Calendar className="h-3 w-3 inline mr-1" />
                    {new Date(review.date).toLocaleDateString()}
                  </span>
                </div>
                
                <p className="text-gray-700 mb-3">{review.comment}</p>
                
                <div className="flex items-center space-x-4 text-sm">
                  <button className="flex items-center space-x-1 text-gray-500 hover:text-green-600 transition-colors">
                    <ThumbsUp className="h-4 w-4" />
                    <span>Helpful ({review.helpful})</span>
                  </button>
                  <button className="flex items-center space-x-1 text-gray-500 hover:text-red-600 transition-colors">
                    <ThumbsDown className="h-4 w-4" />
                    <span>Not Helpful ({review.notHelpful})</span>
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default RatingSystem;
