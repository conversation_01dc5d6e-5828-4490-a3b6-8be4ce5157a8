import React from 'react';
import { motion } from 'framer-motion';
import { Package, Truck, Clock } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  type?: 'default' | 'package' | 'delivery' | 'dots';
  message?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  type = 'default',
  message,
  fullScreen = false 
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const containerClasses = fullScreen 
    ? 'fixed inset-0 bg-white bg-opacity-90 flex items-center justify-center z-50'
    : 'flex items-center justify-center p-4';

  const DefaultSpinner = () => (
    <motion.div
      className={`border-4 border-gray-200 border-t-blue-600 rounded-full ${sizeClasses[size]}`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  );

  const PackageSpinner = () => (
    <motion.div
      className="flex items-center space-x-2"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        animate={{ 
          y: [0, -10, 0],
          rotate: [0, 5, -5, 0]
        }}
        transition={{ 
          duration: 2, 
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <Package className={`text-blue-600 ${sizeClasses[size]}`} />
      </motion.div>
    </motion.div>
  );

  const DeliverySpinner = () => (
    <motion.div
      className="flex items-center space-x-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        animate={{ x: [0, 20, 0] }}
        transition={{ 
          duration: 2, 
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <Truck className={`text-blue-600 ${sizeClasses[size]}`} />
      </motion.div>
      <motion.div
        animate={{ scale: [1, 1.2, 1] }}
        transition={{ 
          duration: 1, 
          repeat: Infinity,
          ease: "easeInOut"
        }}
      >
        <Package className={`text-gray-400 ${sizeClasses[size]}`} />
      </motion.div>
    </motion.div>
  );

  const DotsSpinner = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="w-2 h-2 bg-blue-600 rounded-full"
          animate={{ 
            scale: [1, 1.5, 1],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{ 
            duration: 1, 
            repeat: Infinity,
            delay: index * 0.2,
            ease: "easeInOut"
          }}
        />
      ))}
    </div>
  );

  const renderSpinner = () => {
    switch (type) {
      case 'package':
        return <PackageSpinner />;
      case 'delivery':
        return <DeliverySpinner />;
      case 'dots':
        return <DotsSpinner />;
      default:
        return <DefaultSpinner />;
    }
  };

  return (
    <div className={containerClasses}>
      <div className="text-center">
        {renderSpinner()}
        {message && (
          <motion.p 
            className="mt-4 text-gray-600 text-sm"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            {message}
          </motion.p>
        )}
      </div>
    </div>
  );
};

// Skeleton loading components
export const SkeletonCard: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={`animate-pulse ${className}`}>
    <div className="bg-gray-200 rounded-lg h-4 w-3/4 mb-2"></div>
    <div className="bg-gray-200 rounded-lg h-4 w-1/2 mb-2"></div>
    <div className="bg-gray-200 rounded-lg h-4 w-5/6"></div>
  </div>
);

export const SkeletonTable: React.FC<{ rows?: number }> = ({ rows = 5 }) => (
  <div className="animate-pulse">
    {Array.from({ length: rows }).map((_, index) => (
      <div key={index} className="flex space-x-4 py-3 border-b border-gray-200">
        <div className="bg-gray-200 rounded-lg h-4 w-1/4"></div>
        <div className="bg-gray-200 rounded-lg h-4 w-1/3"></div>
        <div className="bg-gray-200 rounded-lg h-4 w-1/6"></div>
        <div className="bg-gray-200 rounded-lg h-4 w-1/4"></div>
      </div>
    ))}
  </div>
);

// Loading overlay component
export const LoadingOverlay: React.FC<{ 
  isLoading: boolean; 
  children: React.ReactNode;
  message?: string;
}> = ({ isLoading, children, message = 'Loading...' }) => (
  <div className="relative">
    {children}
    {isLoading && (
      <motion.div
        className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <LoadingSpinner type="package" message={message} />
      </motion.div>
    )}
  </div>
);

export default LoadingSpinner;
