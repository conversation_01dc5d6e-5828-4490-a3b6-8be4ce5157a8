import React, { createContext, useContext, useState, ReactNode } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'customer' | 'admin' | 'partner';
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  const login = async (email: string, password: string) => {
    // Mock login - in production, this would make an API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Enhanced admin detection
    const isAdmin = email.toLowerCase().includes('admin') || 
                   email === '<EMAIL>' || 
                   email === '<EMAIL>';
    
    const mockUser: User = {
      id: isAdmin ? 'admin-001' : '1',
      name: isAdmin ? 'Admin User' : 'John Doe',
      email: email,
      role: isAdmin ? 'admin' : 'customer'
    };
    
    setUser(mockUser);
  };

  const register = async (name: string, email: string, password: string) => {
    // Mock registration - in production, this would make an API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockUser: User = {
      id: Date.now().toString(),
      name: name,
      email: email,
      role: 'customer'
    };
    
    setUser(mockUser);
  };

  const logout = () => {
    setUser(null);
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{ user, login, register, logout, isAuthenticated }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};