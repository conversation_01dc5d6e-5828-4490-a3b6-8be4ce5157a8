import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, Plus, X, AlertCircle, CheckCircle } from 'lucide-react';
import { format, addDays, startOfDay, isAfter, isBefore, parseISO } from 'date-fns';

interface TimeSlot {
  id: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  price: number;
  label: string;
}

interface DeliverySchedule {
  date: string;
  timeSlot: TimeSlot;
  isRecurring?: boolean;
  recurringPattern?: 'daily' | 'weekly' | 'monthly';
  recurringEndDate?: string;
}

interface DeliverySchedulerProps {
  onScheduleSelect: (schedule: DeliverySchedule) => void;
  selectedSchedule?: DeliverySchedule;
  minDate?: Date;
  maxDate?: Date;
  className?: string;
}

const DeliveryScheduler: React.FC<DeliverySchedulerProps> = ({
  onScheduleSelect,
  selectedSchedule,
  minDate = new Date(),
  maxDate = addDays(new Date(), 30),
  className = ''
}) => {
  const [selectedDate, setSelectedDate] = useState<string>(
    selectedSchedule?.date || format(new Date(), 'yyyy-MM-dd')
  );
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(
    selectedSchedule?.timeSlot || null
  );
  const [isRecurring, setIsRecurring] = useState(selectedSchedule?.isRecurring || false);
  const [recurringPattern, setRecurringPattern] = useState<'daily' | 'weekly' | 'monthly'>(
    selectedSchedule?.recurringPattern || 'weekly'
  );
  const [recurringEndDate, setRecurringEndDate] = useState<string>(
    selectedSchedule?.recurringEndDate || format(addDays(new Date(), 30), 'yyyy-MM-dd')
  );

  // Generate available dates
  const generateAvailableDates = () => {
    const dates = [];
    let currentDate = startOfDay(minDate);
    const endDate = startOfDay(maxDate);

    while (isBefore(currentDate, endDate) || currentDate.getTime() === endDate.getTime()) {
      dates.push(format(currentDate, 'yyyy-MM-dd'));
      currentDate = addDays(currentDate, 1);
    }

    return dates;
  };

  // Time slots for different delivery types
  const timeSlots: TimeSlot[] = [
    {
      id: 'morning-express',
      startTime: '08:00',
      endTime: '10:00',
      isAvailable: true,
      price: 100,
      label: 'Morning Express'
    },
    {
      id: 'morning-standard',
      startTime: '10:00',
      endTime: '12:00',
      isAvailable: true,
      price: 75,
      label: 'Morning Standard'
    },
    {
      id: 'afternoon-express',
      startTime: '12:00',
      endTime: '14:00',
      isAvailable: true,
      price: 100,
      label: 'Afternoon Express'
    },
    {
      id: 'afternoon-standard',
      startTime: '14:00',
      endTime: '17:00',
      isAvailable: true,
      price: 75,
      label: 'Afternoon Standard'
    },
    {
      id: 'evening-express',
      startTime: '17:00',
      endTime: '19:00',
      isAvailable: true,
      price: 120,
      label: 'Evening Express'
    },
    {
      id: 'evening-standard',
      startTime: '19:00',
      endTime: '21:00',
      isAvailable: true,
      price: 90,
      label: 'Evening Standard'
    }
  ];

  const availableDates = generateAvailableDates();

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setSelectedTimeSlot(null); // Reset time slot when date changes
  };

  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    setSelectedTimeSlot(timeSlot);
    
    const schedule: DeliverySchedule = {
      date: selectedDate,
      timeSlot,
      isRecurring,
      recurringPattern: isRecurring ? recurringPattern : undefined,
      recurringEndDate: isRecurring ? recurringEndDate : undefined
    };
    
    onScheduleSelect(schedule);
  };

  const isDateSelected = (date: string) => date === selectedDate;
  const isTimeSlotSelected = (timeSlot: TimeSlot) => selectedTimeSlot?.id === timeSlot.id;

  const formatDateDisplay = (dateString: string) => {
    const date = parseISO(dateString);
    const today = new Date();
    const tomorrow = addDays(today, 1);

    if (format(date, 'yyyy-MM-dd') === format(today, 'yyyy-MM-dd')) {
      return 'Today';
    } else if (format(date, 'yyyy-MM-dd') === format(tomorrow, 'yyyy-MM-dd')) {
      return 'Tomorrow';
    } else {
      return format(date, 'MMM dd');
    }
  };

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      <div className="flex items-center space-x-2 mb-6">
        <Calendar className="h-6 w-6 text-blue-600" />
        <h3 className="text-xl font-semibold text-gray-800">Schedule Delivery</h3>
      </div>

      {/* Date Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Select Date</h4>
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-7 gap-2">
          {availableDates.slice(0, 14).map((date) => (
            <motion.button
              key={date}
              onClick={() => handleDateSelect(date)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`p-3 rounded-lg border-2 transition-all text-center ${
                isDateSelected(date)
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 hover:border-gray-300 text-gray-700'
              }`}
            >
              <div className="text-xs font-medium">{formatDateDisplay(date)}</div>
              <div className="text-xs text-gray-500 mt-1">
                {format(parseISO(date), 'EEE')}
              </div>
            </motion.button>
          ))}
        </div>
      </div>

      {/* Time Slot Selection */}
      {selectedDate && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <h4 className="text-sm font-medium text-gray-700 mb-3">Select Time Slot</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {timeSlots.map((timeSlot) => (
              <motion.button
                key={timeSlot.id}
                onClick={() => handleTimeSlotSelect(timeSlot)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                disabled={!timeSlot.isAvailable}
                className={`p-4 rounded-lg border-2 transition-all text-left ${
                  isTimeSlotSelected(timeSlot)
                    ? 'border-blue-500 bg-blue-50'
                    : timeSlot.isAvailable
                    ? 'border-gray-200 hover:border-gray-300'
                    : 'border-gray-100 bg-gray-50 cursor-not-allowed'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="font-medium text-gray-800">{timeSlot.label}</div>
                    <div className="text-sm text-gray-600">
                      {timeSlot.startTime} - {timeSlot.endTime}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-blue-600">
                      +₹{timeSlot.price}
                    </div>
                    {!timeSlot.isAvailable && (
                      <div className="text-xs text-red-500">Unavailable</div>
                    )}
                  </div>
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}

      {/* Recurring Delivery Options */}
      {selectedTimeSlot && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <div className="flex items-center space-x-2 mb-3">
            <input
              type="checkbox"
              id="recurring"
              checked={isRecurring}
              onChange={(e) => setIsRecurring(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="recurring" className="text-sm font-medium text-gray-700">
              Make this a recurring delivery
            </label>
          </div>

          {isRecurring && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              className="space-y-4 pl-6 border-l-2 border-blue-200"
            >
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Repeat Pattern
                </label>
                <select
                  value={recurringPattern}
                  onChange={(e) => setRecurringPattern(e.target.value as any)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Date
                </label>
                <input
                  type="date"
                  value={recurringEndDate}
                  onChange={(e) => setRecurringEndDate(e.target.value)}
                  min={format(addDays(parseISO(selectedDate), 1), 'yyyy-MM-dd')}
                  max={format(maxDate, 'yyyy-MM-dd')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </motion.div>
          )}
        </motion.div>
      )}

      {/* Summary */}
      {selectedTimeSlot && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-50 rounded-lg p-4"
        >
          <h4 className="font-semibold text-gray-800 mb-2 flex items-center">
            <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
            Delivery Schedule Summary
          </h4>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Date:</span> {formatDateDisplay(selectedDate)} ({format(parseISO(selectedDate), 'MMM dd, yyyy')})
            </div>
            <div>
              <span className="font-medium">Time:</span> {selectedTimeSlot.startTime} - {selectedTimeSlot.endTime}
            </div>
            <div>
              <span className="font-medium">Type:</span> {selectedTimeSlot.label}
            </div>
            <div>
              <span className="font-medium">Additional Cost:</span> ₹{selectedTimeSlot.price}
            </div>
            {isRecurring && (
              <>
                <div>
                  <span className="font-medium">Recurring:</span> {recurringPattern} until {format(parseISO(recurringEndDate), 'MMM dd, yyyy')}
                </div>
                <div className="text-blue-600 text-xs">
                  <AlertCircle className="h-3 w-3 inline mr-1" />
                  Recurring deliveries can be modified or cancelled anytime
                </div>
              </>
            )}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default DeliveryScheduler;
