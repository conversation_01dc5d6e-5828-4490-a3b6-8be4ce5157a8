import React, { useEffect, useRef, useState } from 'react';

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage?: number;
  networkRequests: number;
  errors: number;
  userInteractions: number;
}

interface PerformanceConfig {
  enableMemoryMonitoring?: boolean;
  enableNetworkMonitoring?: boolean;
  enableUserInteractionTracking?: boolean;
  reportingInterval?: number;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

export const usePerformanceMonitoring = (config: PerformanceConfig = {}) => {
  const {
    enableMemoryMonitoring = true,
    enableNetworkMonitoring = true,
    enableUserInteractionTracking = true,
    reportingInterval = 30000, // 30 seconds
    onMetricsUpdate
  } = config;

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    networkRequests: 0,
    errors: 0,
    userInteractions: 0
  });

  const startTimeRef = useRef<number>(Date.now());
  const networkRequestsRef = useRef<number>(0);
  const errorsRef = useRef<number>(0);
  const userInteractionsRef = useRef<number>(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Measure component render time
  const measureRenderTime = () => {
    const renderStart = performance.now();
    
    return () => {
      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;
      
      setMetrics(prev => ({
        ...prev,
        renderTime: renderTime
      }));
    };
  };

  // Get memory usage (if available)
  const getMemoryUsage = (): number => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / 1024 / 1024; // Convert to MB
    }
    return 0;
  };

  // Monitor network requests
  const monitorNetworkRequests = () => {
    if (!enableNetworkMonitoring) return;

    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      networkRequestsRef.current += 1;
      try {
        const response = await originalFetch(...args);
        return response;
      } catch (error) {
        errorsRef.current += 1;
        throw error;
      }
    };

    // Monitor XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(...args) {
      networkRequestsRef.current += 1;
      return originalXHROpen.apply(this, args);
    };

    return () => {
      window.fetch = originalFetch;
      XMLHttpRequest.prototype.open = originalXHROpen;
    };
  };

  // Monitor user interactions
  const monitorUserInteractions = () => {
    if (!enableUserInteractionTracking) return;

    const handleUserInteraction = () => {
      userInteractionsRef.current += 1;
    };

    const events = ['click', 'keydown', 'scroll', 'touchstart'];
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { passive: true });
    });

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
    };
  };

  // Monitor JavaScript errors
  const monitorErrors = () => {
    const handleError = () => {
      errorsRef.current += 1;
    };

    const handleUnhandledRejection = () => {
      errorsRef.current += 1;
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  };

  // Update metrics periodically
  const updateMetrics = () => {
    const currentTime = Date.now();
    const loadTime = currentTime - startTimeRef.current;

    const newMetrics: PerformanceMetrics = {
      loadTime,
      renderTime: metrics.renderTime,
      memoryUsage: enableMemoryMonitoring ? getMemoryUsage() : undefined,
      networkRequests: networkRequestsRef.current,
      errors: errorsRef.current,
      userInteractions: userInteractionsRef.current
    };

    setMetrics(newMetrics);
    onMetricsUpdate?.(newMetrics);
  };

  // Get Web Vitals
  const getWebVitals = () => {
    const vitals = {
      FCP: 0, // First Contentful Paint
      LCP: 0, // Largest Contentful Paint
      FID: 0, // First Input Delay
      CLS: 0  // Cumulative Layout Shift
    };

    // Use Performance Observer if available
    if ('PerformanceObserver' in window) {
      // Measure FCP
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            vitals.FCP = entry.startTime;
          }
        });
      }).observe({ entryTypes: ['paint'] });

      // Measure LCP
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        vitals.LCP = lastEntry.startTime;
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // Measure FID
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          vitals.FID = entry.processingStart - entry.startTime;
        });
      }).observe({ entryTypes: ['first-input'] });

      // Measure CLS
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            vitals.CLS += entry.value;
          }
        });
      }).observe({ entryTypes: ['layout-shift'] });
    }

    return vitals;
  };

  // Performance optimization suggestions
  const getOptimizationSuggestions = (): string[] => {
    const suggestions: string[] = [];

    if (metrics.loadTime > 3000) {
      suggestions.push('Consider code splitting to reduce initial bundle size');
    }

    if (metrics.renderTime > 100) {
      suggestions.push('Optimize component rendering with React.memo or useMemo');
    }

    if (metrics.memoryUsage && metrics.memoryUsage > 50) {
      suggestions.push('High memory usage detected - check for memory leaks');
    }

    if (metrics.networkRequests > 20) {
      suggestions.push('Consider reducing the number of network requests');
    }

    if (metrics.errors > 0) {
      suggestions.push('JavaScript errors detected - check console for details');
    }

    return suggestions;
  };

  // Initialize monitoring
  useEffect(() => {
    const cleanupFunctions: (() => void)[] = [];

    // Start monitoring
    cleanupFunctions.push(monitorNetworkRequests() || (() => {}));
    cleanupFunctions.push(monitorUserInteractions() || (() => {}));
    cleanupFunctions.push(monitorErrors());

    // Start periodic updates
    intervalRef.current = setInterval(updateMetrics, reportingInterval);

    // Initial metrics update
    updateMetrics();

    return () => {
      // Cleanup
      cleanupFunctions.forEach(cleanup => cleanup());
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    metrics,
    measureRenderTime,
    getWebVitals,
    getOptimizationSuggestions,
    updateMetrics
  };
};

// Performance monitoring component
export const PerformanceMonitor: React.FC<{
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
  showDebugInfo?: boolean;
}> = ({ onMetricsUpdate, showDebugInfo = false }) => {
  const { metrics, getOptimizationSuggestions } = usePerformanceMonitoring({
    onMetricsUpdate
  });

  if (!showDebugInfo || process.env.NODE_ENV !== 'development') {
    return null;
  }

  const suggestions = getOptimizationSuggestions();

  return React.createElement('div', {
    className: "fixed bottom-4 left-4 bg-black bg-opacity-80 text-white p-4 rounded-lg text-xs max-w-sm z-50"
  }, [
    React.createElement('h4', { key: 'title', className: "font-bold mb-2" }, 'Performance Metrics'),
    React.createElement('div', { key: 'metrics', className: "space-y-1" }, [
      React.createElement('div', { key: 'load' }, `Load Time: ${metrics.loadTime}ms`),
      React.createElement('div', { key: 'render' }, `Render Time: ${metrics.renderTime.toFixed(2)}ms`),
      metrics.memoryUsage && React.createElement('div', { key: 'memory' }, `Memory: ${metrics.memoryUsage.toFixed(2)}MB`),
      React.createElement('div', { key: 'network' }, `Network Requests: ${metrics.networkRequests}`),
      React.createElement('div', { key: 'errors' }, `Errors: ${metrics.errors}`),
      React.createElement('div', { key: 'interactions' }, `Interactions: ${metrics.userInteractions}`)
    ].filter(Boolean)),
    suggestions.length > 0 && React.createElement('div', { key: 'suggestions', className: "mt-3 pt-2 border-t border-gray-600" }, [
      React.createElement('h5', { key: 'suggestions-title', className: "font-bold mb-1" }, 'Suggestions:'),
      React.createElement('ul', { key: 'suggestions-list', className: "space-y-1" },
        suggestions.map((suggestion, index) =>
          React.createElement('li', { key: index, className: "text-yellow-300" }, `• ${suggestion}`)
        )
      )
    ])
  ].filter(Boolean));
};

export default usePerformanceMonitoring;
