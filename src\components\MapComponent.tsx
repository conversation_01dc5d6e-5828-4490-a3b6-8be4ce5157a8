import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import { Icon, LatLngExpression } from 'leaflet';
import { Package, Truck, MapPin, Navigation } from 'lucide-react';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in React Leaflet
import markerIcon from 'leaflet/dist/images/marker-icon.png';
import markerIcon2x from 'leaflet/dist/images/marker-icon-2x.png';
import markerShadow from 'leaflet/dist/images/marker-shadow.png';

delete (Icon.Default.prototype as any)._getIconUrl;
Icon.Default.mergeOptions({
  iconRetinaUrl: markerIcon2x,
  iconUrl: markerIcon,
  shadowUrl: markerShadow,
});

interface Location {
  lat: number;
  lng: number;
  address?: string;
}

interface DeliveryMapProps {
  pickupLocation: Location;
  deliveryLocation: Location;
  currentLocation?: Location;
  route?: Location[];
  showRoute?: boolean;
  height?: string;
  className?: string;
}

// Custom icons for different marker types
const createCustomIcon = (color: string, iconType: 'pickup' | 'delivery' | 'current') => {
  const iconHtml = `
    <div style="
      background-color: ${color};
      width: 30px;
      height: 30px;
      border-radius: 50%;
      border: 3px solid white;
      box-shadow: 0 2px 4px rgba(0,0,0,0.3);
      display: flex;
      align-items: center;
      justify-content: center;
    ">
      <div style="color: white; font-size: 12px;">
        ${iconType === 'pickup' ? '📦' : iconType === 'delivery' ? '🏠' : '🚚'}
      </div>
    </div>
  `;

  return new Icon({
    iconUrl: `data:image/svg+xml;base64,${btoa(iconHtml)}`,
    iconSize: [30, 30],
    iconAnchor: [15, 15],
    popupAnchor: [0, -15],
  });
};

// Component to fit map bounds to show all markers
const FitBounds: React.FC<{ locations: Location[] }> = ({ locations }) => {
  const map = useMap();

  useEffect(() => {
    if (locations.length > 0) {
      const bounds = locations.map(loc => [loc.lat, loc.lng] as LatLngExpression);
      map.fitBounds(bounds, { padding: [20, 20] });
    }
  }, [locations, map]);

  return null;
};

export const DeliveryMap: React.FC<DeliveryMapProps> = ({
  pickupLocation,
  deliveryLocation,
  currentLocation,
  route,
  showRoute = true,
  height = '400px',
  className = ''
}) => {
  const [mapCenter, setMapCenter] = useState<LatLngExpression>([
    (pickupLocation.lat + deliveryLocation.lat) / 2,
    (pickupLocation.lng + deliveryLocation.lng) / 2
  ]);

  const allLocations = [pickupLocation, deliveryLocation];
  if (currentLocation) allLocations.push(currentLocation);

  // Default route (straight line between pickup and delivery)
  const defaultRoute = [
    [pickupLocation.lat, pickupLocation.lng],
    [deliveryLocation.lat, deliveryLocation.lng]
  ] as LatLngExpression[];

  const routeToShow = route?.map(loc => [loc.lat, loc.lng] as LatLngExpression) || defaultRoute;

  return (
    <div className={`relative ${className}`} style={{ height }}>
      <MapContainer
        center={mapCenter}
        zoom={13}
        style={{ height: '100%', width: '100%' }}
        className="rounded-lg"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Pickup Location Marker */}
        <Marker
          position={[pickupLocation.lat, pickupLocation.lng]}
          icon={createCustomIcon('#10B981', 'pickup')}
        >
          <Popup>
            <div className="text-center">
              <div className="flex items-center space-x-2 mb-2">
                <Package className="h-4 w-4 text-green-600" />
                <span className="font-semibold">Pickup Location</span>
              </div>
              <p className="text-sm text-gray-600">
                {pickupLocation.address || `${pickupLocation.lat}, ${pickupLocation.lng}`}
              </p>
            </div>
          </Popup>
        </Marker>

        {/* Delivery Location Marker */}
        <Marker
          position={[deliveryLocation.lat, deliveryLocation.lng]}
          icon={createCustomIcon('#3B82F6', 'delivery')}
        >
          <Popup>
            <div className="text-center">
              <div className="flex items-center space-x-2 mb-2">
                <MapPin className="h-4 w-4 text-blue-600" />
                <span className="font-semibold">Delivery Location</span>
              </div>
              <p className="text-sm text-gray-600">
                {deliveryLocation.address || `${deliveryLocation.lat}, ${deliveryLocation.lng}`}
              </p>
            </div>
          </Popup>
        </Marker>

        {/* Current Location Marker (if available) */}
        {currentLocation && (
          <Marker
            position={[currentLocation.lat, currentLocation.lng]}
            icon={createCustomIcon('#F59E0B', 'current')}
          >
            <Popup>
              <div className="text-center">
                <div className="flex items-center space-x-2 mb-2">
                  <Truck className="h-4 w-4 text-yellow-600" />
                  <span className="font-semibold">Current Location</span>
                </div>
                <p className="text-sm text-gray-600">
                  {currentLocation.address || `${currentLocation.lat}, ${currentLocation.lng}`}
                </p>
              </div>
            </Popup>
          </Marker>
        )}

        {/* Route Line */}
        {showRoute && (
          <Polyline
            positions={routeToShow}
            color="#3B82F6"
            weight={4}
            opacity={0.7}
            dashArray="10, 10"
          />
        )}

        {/* Fit bounds to show all markers */}
        <FitBounds locations={allLocations} />
      </MapContainer>

      {/* Map Controls */}
      <div className="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-2 space-y-2">
        <button
          onClick={() => {
            // Center on current location if available, otherwise center between pickup and delivery
            const centerLoc = currentLocation || {
              lat: (pickupLocation.lat + deliveryLocation.lat) / 2,
              lng: (pickupLocation.lng + deliveryLocation.lng) / 2
            };
            setMapCenter([centerLoc.lat, centerLoc.lng]);
          }}
          className="flex items-center space-x-1 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded transition-colors"
        >
          <Navigation className="h-4 w-4" />
          <span>Center</span>
        </button>
      </div>
    </div>
  );
};

// Simple location picker component
interface LocationPickerProps {
  onLocationSelect: (location: Location) => void;
  initialLocation?: Location;
  height?: string;
}

export const LocationPicker: React.FC<LocationPickerProps> = ({
  onLocationSelect,
  initialLocation,
  height = '300px'
}) => {
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    initialLocation || null
  );

  const defaultCenter: LatLngExpression = initialLocation 
    ? [initialLocation.lat, initialLocation.lng]
    : [28.6139, 77.2090]; // Delhi coordinates as default

  const handleMapClick = (e: any) => {
    const { lat, lng } = e.latlng;
    const newLocation = { lat, lng };
    setSelectedLocation(newLocation);
    onLocationSelect(newLocation);
  };

  return (
    <div className="relative" style={{ height }}>
      <MapContainer
        center={defaultCenter}
        zoom={13}
        style={{ height: '100%', width: '100%' }}
        className="rounded-lg"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {selectedLocation && (
          <Marker position={[selectedLocation.lat, selectedLocation.lng]}>
            <Popup>
              <div className="text-center">
                <p className="font-semibold">Selected Location</p>
                <p className="text-sm text-gray-600">
                  {selectedLocation.lat.toFixed(6)}, {selectedLocation.lng.toFixed(6)}
                </p>
              </div>
            </Popup>
          </Marker>
        )}

        {/* Map click handler */}
        <div onClick={handleMapClick} style={{ height: '100%', width: '100%' }} />
      </MapContainer>

      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-lg p-3">
        <p className="text-sm text-gray-600">Click on the map to select a location</p>
      </div>
    </div>
  );
};

export default DeliveryMap;
