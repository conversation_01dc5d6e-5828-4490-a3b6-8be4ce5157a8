import React from 'react';
import { Toaster, toast } from 'react-hot-toast';
import { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-react';

// Custom toast component
const CustomToast = ({ 
  type, 
  title, 
  message, 
  onDismiss 
}: { 
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  onDismiss: () => void;
}) => {
  const icons = {
    success: <CheckCircle className="h-5 w-5 text-green-500" />,
    error: <XCircle className="h-5 w-5 text-red-500" />,
    warning: <AlertTriangle className="h-5 w-5 text-yellow-500" />,
    info: <Info className="h-5 w-5 text-blue-500" />
  };

  const bgColors = {
    success: 'bg-green-50 border-green-200',
    error: 'bg-red-50 border-red-200',
    warning: 'bg-yellow-50 border-yellow-200',
    info: 'bg-blue-50 border-blue-200'
  };

  return (
    <div className={`max-w-md w-full ${bgColors[type]} border rounded-lg shadow-lg p-4`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {icons[type]}
        </div>
        <div className="ml-3 flex-1">
          <p className="text-sm font-medium text-gray-900">{title}</p>
          <p className="mt-1 text-sm text-gray-600">{message}</p>
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            onClick={onDismiss}
            className="inline-flex text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Toast utility functions
export const showToast = {
  success: (title: string, message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="success"
        title={title}
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ), {
      duration: 4000,
      position: 'top-right'
    });
  },

  error: (title: string, message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="error"
        title={title}
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ), {
      duration: 6000,
      position: 'top-right'
    });
  },

  warning: (title: string, message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="warning"
        title={title}
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ), {
      duration: 5000,
      position: 'top-right'
    });
  },

  info: (title: string, message: string) => {
    toast.custom((t) => (
      <CustomToast
        type="info"
        title={title}
        message={message}
        onDismiss={() => toast.dismiss(t.id)}
      />
    ), {
      duration: 4000,
      position: 'top-right'
    });
  },

  promise: <T,>(
    promise: Promise<T>,
    {
      loading,
      success,
      error,
    }: {
      loading: string;
      success: string;
      error: string;
    }
  ) => {
    return toast.promise(promise, {
      loading,
      success,
      error,
    });
  }
};

// Main Toast Provider Component
export const ToastProvider: React.FC = () => {
  return (
    <Toaster
      position="top-right"
      reverseOrder={false}
      gutter={8}
      containerClassName=""
      containerStyle={{}}
      toastOptions={{
        // Default options for all toasts
        className: '',
        duration: 4000,
        style: {
          background: '#fff',
          color: '#363636',
        },
        
        // Default options for specific types
        success: {
          duration: 3000,
          iconTheme: {
            primary: '#10B981',
            secondary: '#fff',
          },
        },
        error: {
          duration: 5000,
          iconTheme: {
            primary: '#EF4444',
            secondary: '#fff',
          },
        },
      }}
    />
  );
};

export default ToastProvider;
