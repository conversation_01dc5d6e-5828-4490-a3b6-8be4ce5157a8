import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter, X, Calendar, Package, Truck, Clock } from 'lucide-react';

interface SearchBarProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  onSearch?: (value: string) => void;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = 'Search...',
  value,
  onChange,
  onSearch,
  className = ''
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.(value);
  };

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`}>
      <motion.div
        className={`
          relative flex items-center border rounded-lg transition-all duration-200
          ${isFocused ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-300'}
        `}
        whileFocus={{ scale: 1.02 }}
      >
        <Search className="absolute left-3 h-5 w-5 text-gray-400" />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          className="w-full pl-10 pr-4 py-3 bg-transparent focus:outline-none"
        />
        {value && (
          <button
            type="button"
            onClick={() => onChange('')}
            className="absolute right-3 text-gray-400 hover:text-gray-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </motion.div>
    </form>
  );
};

interface FilterOption {
  id: string;
  label: string;
  value: any;
  icon?: React.ReactNode;
}

interface FilterGroupProps {
  title: string;
  options: FilterOption[];
  selectedValues: any[];
  onChange: (values: any[]) => void;
  multiSelect?: boolean;
}

export const FilterGroup: React.FC<FilterGroupProps> = ({
  title,
  options,
  selectedValues,
  onChange,
  multiSelect = true
}) => {
  const handleOptionChange = (option: FilterOption) => {
    if (multiSelect) {
      const isSelected = selectedValues.includes(option.value);
      if (isSelected) {
        onChange(selectedValues.filter(v => v !== option.value));
      } else {
        onChange([...selectedValues, option.value]);
      }
    } else {
      onChange([option.value]);
    }
  };

  return (
    <div className="space-y-3">
      <h3 className="font-medium text-gray-900">{title}</h3>
      <div className="space-y-2">
        {options.map((option) => {
          const isSelected = selectedValues.includes(option.value);
          return (
            <motion.label
              key={option.id}
              className="flex items-center space-x-3 cursor-pointer"
              whileHover={{ x: 2 }}
              whileTap={{ scale: 0.98 }}
            >
              <input
                type={multiSelect ? 'checkbox' : 'radio'}
                checked={isSelected}
                onChange={() => handleOptionChange(option)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              {option.icon && (
                <span className="text-gray-500">{option.icon}</span>
              )}
              <span className="text-sm text-gray-700">{option.label}</span>
            </motion.label>
          );
        })}
      </div>
    </div>
  );
};

interface FilterPanelProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  onApply?: () => void;
  onReset?: () => void;
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  isOpen,
  onClose,
  children,
  onApply,
  onReset
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            onClick={onClose}
          />
          
          {/* Panel */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Filters</h2>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
              
              <div className="space-y-6">
                {children}
              </div>
              
              {(onApply || onReset) && (
                <div className="flex space-x-3 mt-8 pt-6 border-t border-gray-200">
                  {onReset && (
                    <button
                      onClick={onReset}
                      className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Reset
                    </button>
                  )}
                  {onApply && (
                    <button
                      onClick={onApply}
                      className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Apply
                    </button>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

interface QuickFiltersProps {
  filters: FilterOption[];
  selectedFilters: any[];
  onChange: (filters: any[]) => void;
  className?: string;
}

export const QuickFilters: React.FC<QuickFiltersProps> = ({
  filters,
  selectedFilters,
  onChange,
  className = ''
}) => {
  const toggleFilter = (filterValue: any) => {
    const isSelected = selectedFilters.includes(filterValue);
    if (isSelected) {
      onChange(selectedFilters.filter(f => f !== filterValue));
    } else {
      onChange([...selectedFilters, filterValue]);
    }
  };

  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {filters.map((filter) => {
        const isSelected = selectedFilters.includes(filter.value);
        return (
          <motion.button
            key={filter.id}
            onClick={() => toggleFilter(filter.value)}
            className={`
              flex items-center space-x-2 px-3 py-2 rounded-full text-sm font-medium transition-all
              ${isSelected
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }
            `}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {filter.icon && <span>{filter.icon}</span>}
            <span>{filter.label}</span>
            {isSelected && <X className="h-3 w-3" />}
          </motion.button>
        );
      })}
    </div>
  );
};

// Predefined filter options for common use cases
export const packageStatusFilters: FilterOption[] = [
  { id: 'pending', label: 'Pending', value: 'pending', icon: <Clock className="h-4 w-4" /> },
  { id: 'confirmed', label: 'Confirmed', value: 'confirmed', icon: <Package className="h-4 w-4" /> },
  { id: 'picked_up', label: 'Picked Up', value: 'picked_up', icon: <Truck className="h-4 w-4" /> },
  { id: 'in_transit', label: 'In Transit', value: 'in_transit', icon: <Truck className="h-4 w-4" /> },
  { id: 'delivered', label: 'Delivered', value: 'delivered', icon: <Package className="h-4 w-4" /> },
];

export const packageTypeFilters: FilterOption[] = [
  { id: 'documents', label: 'Documents', value: 'documents' },
  { id: 'electronics', label: 'Electronics', value: 'electronics' },
  { id: 'clothing', label: 'Clothing', value: 'clothing' },
  { id: 'food', label: 'Food Items', value: 'food' },
  { id: 'fragile', label: 'Fragile Items', value: 'fragile' },
  { id: 'other', label: 'Other', value: 'other' },
];

export const packageSizeFilters: FilterOption[] = [
  { id: 'small', label: 'Small', value: 'small' },
  { id: 'medium', label: 'Medium', value: 'medium' },
  { id: 'large', label: 'Large', value: 'large' },
  { id: 'extra-large', label: 'Extra Large', value: 'extra-large' },
];
