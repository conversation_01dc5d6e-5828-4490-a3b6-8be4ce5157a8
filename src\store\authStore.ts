import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'customer' | 'admin' | 'partner';
  phone?: string;
  avatar?: string;
  isVerified?: boolean;
  createdAt?: string;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, phone?: string) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Enhanced admin detection
          const isAdmin = email.toLowerCase().includes('admin') || 
                         email === '<EMAIL>' || 
                         email === '<EMAIL>';
          
          const isPartner = email.toLowerCase().includes('partner') ||
                           email.toLowerCase().includes('delivery');
          
          let role: 'customer' | 'admin' | 'partner' = 'customer';
          if (isAdmin) role = 'admin';
          else if (isPartner) role = 'partner';
          
          const mockUser: User = {
            id: isAdmin ? 'admin-001' : isPartner ? 'partner-001' : 'user-001',
            name: isAdmin ? 'Admin User' : isPartner ? 'Delivery Partner' : 'John Doe',
            email: email,
            role: role,
            phone: '+****************',
            isVerified: true,
            createdAt: new Date().toISOString()
          };
          
          set({ 
            user: mockUser, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error) {
          set({ 
            error: 'Invalid credentials. Please try again.', 
            isLoading: false 
          });
          throw error;
        }
      },

      register: async (name: string, email: string, password: string, phone?: string) => {
        set({ isLoading: true, error: null });
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1500));
          
          const newUser: User = {
            id: 'user-' + Date.now(),
            name,
            email,
            role: 'customer',
            phone,
            isVerified: false,
            createdAt: new Date().toISOString()
          };
          
          set({ 
            user: newUser, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error) {
          set({ 
            error: 'Registration failed. Please try again.', 
            isLoading: false 
          });
          throw error;
        }
      },

      logout: () => {
        set({ 
          user: null, 
          isAuthenticated: false, 
          error: null 
        });
      },

      updateProfile: (updates: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({ 
            user: { ...user, ...updates } 
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);
