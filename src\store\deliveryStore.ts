import { create } from 'zustand';

export interface DeliveryPartner {
  id: string;
  name: string;
  phone: string;
  rating: number;
  vehicleType: string;
  currentLocation?: {
    lat: number;
    lng: number;
  };
  isAvailable: boolean;
}

export interface Package {
  id: string;
  trackingId: string;
  userId: string;
  status: 'pending' | 'confirmed' | 'picked_up' | 'in_transit' | 'delivered' | 'cancelled';
  pickupAddress: string;
  deliveryAddress: string;
  pickupContact: string;
  deliveryContact: string;
  packageType: string;
  packageSize: string;
  packageWeight: string;
  deliveryTime: string;
  specialInstructions?: string;
  paymentMethod: string;
  cost: number;
  deliveryPartner?: DeliveryPartner;
  createdAt: string;
  updatedAt: string;
  estimatedDelivery?: string;
  actualDelivery?: string;
  timeline: {
    timestamp: string;
    status: string;
    location?: string;
    description: string;
  }[];
}

interface DeliveryState {
  packages: Package[];
  currentPackage: Package | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  createPackage: (packageData: Omit<Package, 'id' | 'trackingId' | 'createdAt' | 'updatedAt' | 'timeline'>) => Promise<string>;
  getPackage: (trackingId: string) => Promise<Package | null>;
  getUserPackages: (userId: string) => Package[];
  updatePackageStatus: (trackingId: string, status: Package['status'], location?: string) => void;
  assignDeliveryPartner: (trackingId: string, partner: DeliveryPartner) => void;
  addTimelineEvent: (trackingId: string, event: Omit<Package['timeline'][0], 'timestamp'>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useDeliveryStore = create<DeliveryState>((set, get) => ({
  packages: [],
  currentPackage: null,
  isLoading: false,
  error: null,

  createPackage: async (packageData) => {
    set({ isLoading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const trackingId = 'QD' + Date.now().toString().slice(-8);
      const now = new Date().toISOString();
      
      const newPackage: Package = {
        ...packageData,
        id: 'pkg-' + Date.now(),
        trackingId,
        status: 'pending',
        cost: calculateDeliveryCost(packageData.packageSize, packageData.packageWeight),
        createdAt: now,
        updatedAt: now,
        timeline: [{
          timestamp: now,
          status: 'pending',
          description: 'Package booking confirmed. Looking for delivery partner.'
        }]
      };
      
      set(state => ({
        packages: [...state.packages, newPackage],
        currentPackage: newPackage,
        isLoading: false
      }));
      
      return trackingId;
    } catch (error) {
      set({ 
        error: 'Failed to create package. Please try again.', 
        isLoading: false 
      });
      throw error;
    }
  },

  getPackage: async (trackingId: string) => {
    set({ isLoading: true, error: null });
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const { packages } = get();
      let foundPackage = packages.find(pkg => pkg.trackingId === trackingId);
      
      // If not found in store, simulate fetching from API
      if (!foundPackage) {
        foundPackage = generateMockPackage(trackingId);
        set(state => ({
          packages: [...state.packages, foundPackage!]
        }));
      }
      
      set({ 
        currentPackage: foundPackage || null, 
        isLoading: false 
      });
      
      return foundPackage || null;
    } catch (error) {
      set({ 
        error: 'Failed to fetch package information.', 
        isLoading: false 
      });
      return null;
    }
  },

  getUserPackages: (userId: string) => {
    const { packages } = get();
    return packages.filter(pkg => pkg.userId === userId);
  },

  updatePackageStatus: (trackingId: string, status: Package['status'], location?: string) => {
    set(state => ({
      packages: state.packages.map(pkg => 
        pkg.trackingId === trackingId 
          ? { 
              ...pkg, 
              status, 
              updatedAt: new Date().toISOString(),
              timeline: [
                ...pkg.timeline,
                {
                  timestamp: new Date().toISOString(),
                  status,
                  location,
                  description: getStatusDescription(status)
                }
              ]
            }
          : pkg
      )
    }));
  },

  assignDeliveryPartner: (trackingId: string, partner: DeliveryPartner) => {
    set(state => ({
      packages: state.packages.map(pkg => 
        pkg.trackingId === trackingId 
          ? { 
              ...pkg, 
              deliveryPartner: partner,
              status: 'confirmed',
              updatedAt: new Date().toISOString(),
              timeline: [
                ...pkg.timeline,
                {
                  timestamp: new Date().toISOString(),
                  status: 'confirmed',
                  description: `Delivery partner ${partner.name} assigned to your package.`
                }
              ]
            }
          : pkg
      )
    }));
  },

  addTimelineEvent: (trackingId: string, event: Omit<Package['timeline'][0], 'timestamp'>) => {
    set(state => ({
      packages: state.packages.map(pkg => 
        pkg.trackingId === trackingId 
          ? { 
              ...pkg, 
              timeline: [
                ...pkg.timeline,
                {
                  ...event,
                  timestamp: new Date().toISOString()
                }
              ]
            }
          : pkg
      )
    }));
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  }
}));

// Helper functions
function calculateDeliveryCost(size: string, weight: string): number {
  const basePrice = 50;
  const sizeMultiplier = size === 'large' ? 2 : size === 'medium' ? 1.5 : 1;
  const weightMultiplier = parseFloat(weight) > 5 ? 1.5 : 1;
  return Math.round(basePrice * sizeMultiplier * weightMultiplier);
}

function getStatusDescription(status: Package['status']): string {
  const descriptions = {
    pending: 'Package booking confirmed. Looking for delivery partner.',
    confirmed: 'Delivery partner assigned. Preparing for pickup.',
    picked_up: 'Package picked up from sender.',
    in_transit: 'Package is on the way to destination.',
    delivered: 'Package delivered successfully.',
    cancelled: 'Delivery cancelled.'
  };
  return descriptions[status];
}

function generateMockPackage(trackingId: string): Package {
  const statuses: Package['status'][] = ['confirmed', 'picked_up', 'in_transit'];
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
  
  return {
    id: 'pkg-mock-' + Date.now(),
    trackingId,
    userId: 'user-001',
    status: randomStatus,
    pickupAddress: '123 Main St, Downtown',
    deliveryAddress: '456 Oak Ave, Uptown',
    pickupContact: '+****************',
    deliveryContact: '+****************',
    packageType: 'Electronics',
    packageSize: 'medium',
    packageWeight: '2',
    deliveryTime: 'standard',
    paymentMethod: 'card',
    cost: 75,
    deliveryPartner: {
      id: 'partner-001',
      name: 'John Smith',
      phone: '+****************',
      rating: 4.8,
      vehicleType: 'bike',
      isAvailable: true
    },
    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    estimatedDelivery: '2-3 hours',
    timeline: [
      {
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        status: 'pending',
        description: 'Package booking confirmed.'
      },
      {
        timestamp: new Date(Date.now() - 90 * 60 * 1000).toISOString(),
        status: 'confirmed',
        description: 'Delivery partner assigned.'
      }
    ]
  };
}
