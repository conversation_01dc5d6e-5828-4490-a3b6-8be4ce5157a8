import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { useAuthStore } from '../store/authStore';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          useAuthStore.getState().logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic API methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<T>(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<T>(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<T>(url);
    return response.data;
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.post('/auth/login', { email, password });
  }

  async register(userData: any) {
    return this.post('/auth/register', userData);
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  async getCurrentUser() {
    return this.get('/auth/me');
  }

  // Package endpoints
  async createPackage(packageData: any) {
    return this.post('/packages', packageData);
  }

  async getPackage(trackingId: string) {
    return this.get(`/packages/${trackingId}`);
  }

  async getUserPackages(userId: string) {
    return this.get(`/packages/user/${userId}`);
  }

  async updatePackageStatus(trackingId: string, status: string) {
    return this.put(`/packages/${trackingId}/status`, { status });
  }

  // Partner endpoints
  async getAvailablePartners(location: any) {
    return this.get('/partners/available', location);
  }

  async assignPartner(packageId: string, partnerId: string) {
    return this.post(`/packages/${packageId}/assign`, { partnerId });
  }

  // Admin endpoints
  async getAdminStats() {
    return this.get('/admin/stats');
  }

  async getAllPackages() {
    return this.get('/admin/packages');
  }

  async getAllPartners() {
    return this.get('/admin/partners');
  }

  // Notification endpoints
  async getUserNotifications(userId: string) {
    return this.get(`/notifications/user/${userId}`);
  }

  async markNotificationRead(notificationId: string) {
    return this.put(`/notifications/${notificationId}/read`);
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();



// Always use real API service - no more mock data
export const api = apiService;
