import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { useAuthStore } from '../store/authStore';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const { user } = useAuthStore.getState();
        if (user) {
          config.headers.Authorization = `Bearer ${user.id}`; // In real app, use JWT token
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Handle unauthorized access
          useAuthStore.getState().logout();
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Generic API methods
  async get<T>(url: string, params?: any): Promise<T> {
    const response = await this.api.get<T>(url, { params });
    return response.data;
  }

  async post<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.post<T>(url, data);
    return response.data;
  }

  async put<T>(url: string, data?: any): Promise<T> {
    const response = await this.api.put<T>(url, data);
    return response.data;
  }

  async delete<T>(url: string): Promise<T> {
    const response = await this.api.delete<T>(url);
    return response.data;
  }

  // Auth endpoints
  async login(email: string, password: string) {
    return this.post('/auth/login', { email, password });
  }

  async register(userData: any) {
    return this.post('/auth/register', userData);
  }

  async logout() {
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  // Package endpoints
  async createPackage(packageData: any) {
    return this.post('/packages', packageData);
  }

  async getPackage(trackingId: string) {
    return this.get(`/packages/${trackingId}`);
  }

  async getUserPackages(userId: string) {
    return this.get(`/packages/user/${userId}`);
  }

  async updatePackageStatus(trackingId: string, status: string) {
    return this.put(`/packages/${trackingId}/status`, { status });
  }

  // Partner endpoints
  async getAvailablePartners(location: any) {
    return this.get('/partners/available', location);
  }

  async assignPartner(packageId: string, partnerId: string) {
    return this.post(`/packages/${packageId}/assign`, { partnerId });
  }

  // Admin endpoints
  async getAdminStats() {
    return this.get('/admin/stats');
  }

  async getAllPackages() {
    return this.get('/admin/packages');
  }

  async getAllPartners() {
    return this.get('/admin/partners');
  }

  // Notification endpoints
  async getUserNotifications(userId: string) {
    return this.get(`/notifications/user/${userId}`);
  }

  async markNotificationRead(notificationId: string) {
    return this.put(`/notifications/${notificationId}/read`);
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Mock API responses for development
export const mockApiService = {
  async login(email: string, password: string) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      user: {
        id: 'user-123',
        email,
        name: 'John Doe',
        role: email.includes('admin') ? 'admin' : 'customer'
      },
      token: 'mock-jwt-token'
    };
  },

  async register(userData: any) {
    await new Promise(resolve => setTimeout(resolve, 1500));
    return {
      user: {
        id: 'user-' + Date.now(),
        ...userData,
        role: 'customer'
      },
      token: 'mock-jwt-token'
    };
  },

  async createPackage(packageData: any) {
    await new Promise(resolve => setTimeout(resolve, 2000));
    return {
      id: 'pkg-' + Date.now(),
      trackingId: 'QD' + Date.now().toString().slice(-8),
      ...packageData,
      status: 'pending',
      createdAt: new Date().toISOString()
    };
  },

  async getPackage(trackingId: string) {
    await new Promise(resolve => setTimeout(resolve, 1500));
    return {
      trackingId,
      status: 'in_transit',
      pickupAddress: '123 Main St',
      deliveryAddress: '456 Oak Ave',
      estimatedDelivery: '2-3 hours',
      deliveryPartner: {
        name: 'John Smith',
        phone: '******-0123',
        rating: 4.8
      }
    };
  }
};

// Use mock service in development
export const api = process.env.NODE_ENV === 'development' ? mockApiService : apiService;
