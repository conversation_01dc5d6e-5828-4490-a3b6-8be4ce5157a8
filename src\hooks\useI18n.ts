import React, { createContext, useContext, useState, useEffect } from 'react';

// Supported languages
export type Language = 'en' | 'es' | 'fr' | 'de' | 'hi' | 'zh';

// Translation keys structure
export interface Translations {
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    delete: string;
    edit: string;
    view: string;
    search: string;
    filter: string;
    clear: string;
    back: string;
    next: string;
    previous: string;
    close: string;
    submit: string;
    retry: string;
  };
  navigation: {
    home: string;
    dashboard: string;
    bookDelivery: string;
    trackPackage: string;
    login: string;
    register: string;
    logout: string;
    profile: string;
    settings: string;
    help: string;
    contact: string;
  };
  auth: {
    loginTitle: string;
    registerTitle: string;
    email: string;
    password: string;
    confirmPassword: string;
    name: string;
    phone: string;
    forgotPassword: string;
    rememberMe: string;
    alreadyHaveAccount: string;
    dontHaveAccount: string;
    loginSuccess: string;
    loginError: string;
    registerSuccess: string;
    registerError: string;
  };
  delivery: {
    bookDelivery: string;
    trackPackage: string;
    pickupAddress: string;
    deliveryAddress: string;
    packageType: string;
    packageSize: string;
    packageWeight: string;
    deliveryTime: string;
    specialInstructions: string;
    paymentMethod: string;
    trackingId: string;
    status: string;
    estimatedDelivery: string;
    deliveryPartner: string;
    cost: string;
    timeline: string;
  };
  status: {
    pending: string;
    confirmed: string;
    pickedUp: string;
    inTransit: string;
    delivered: string;
    cancelled: string;
  };
  errors: {
    required: string;
    invalidEmail: string;
    invalidPhone: string;
    passwordTooShort: string;
    passwordsNotMatch: string;
    networkError: string;
    serverError: string;
    notFound: string;
    unauthorized: string;
    forbidden: string;
  };
}

// English translations (default)
const enTranslations: Translations = {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    view: 'View',
    search: 'Search',
    filter: 'Filter',
    clear: 'Clear',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    submit: 'Submit',
    retry: 'Retry',
  },
  navigation: {
    home: 'Home',
    dashboard: 'Dashboard',
    bookDelivery: 'Book Delivery',
    trackPackage: 'Track Package',
    login: 'Login',
    register: 'Register',
    logout: 'Logout',
    profile: 'Profile',
    settings: 'Settings',
    help: 'Help',
    contact: 'Contact',
  },
  auth: {
    loginTitle: 'Sign in to your account',
    registerTitle: 'Create your account',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    name: 'Full Name',
    phone: 'Phone Number',
    forgotPassword: 'Forgot your password?',
    rememberMe: 'Remember me',
    alreadyHaveAccount: 'Already have an account?',
    dontHaveAccount: "Don't have an account?",
    loginSuccess: 'Login successful',
    loginError: 'Login failed',
    registerSuccess: 'Registration successful',
    registerError: 'Registration failed',
  },
  delivery: {
    bookDelivery: 'Book Delivery',
    trackPackage: 'Track Package',
    pickupAddress: 'Pickup Address',
    deliveryAddress: 'Delivery Address',
    packageType: 'Package Type',
    packageSize: 'Package Size',
    packageWeight: 'Package Weight',
    deliveryTime: 'Delivery Time',
    specialInstructions: 'Special Instructions',
    paymentMethod: 'Payment Method',
    trackingId: 'Tracking ID',
    status: 'Status',
    estimatedDelivery: 'Estimated Delivery',
    deliveryPartner: 'Delivery Partner',
    cost: 'Cost',
    timeline: 'Timeline',
  },
  status: {
    pending: 'Pending',
    confirmed: 'Confirmed',
    pickedUp: 'Picked Up',
    inTransit: 'In Transit',
    delivered: 'Delivered',
    cancelled: 'Cancelled',
  },
  errors: {
    required: 'This field is required',
    invalidEmail: 'Please enter a valid email address',
    invalidPhone: 'Please enter a valid phone number',
    passwordTooShort: 'Password must be at least 8 characters',
    passwordsNotMatch: 'Passwords do not match',
    networkError: 'Network error occurred',
    serverError: 'Server error occurred',
    notFound: 'Resource not found',
    unauthorized: 'Unauthorized access',
    forbidden: 'Access forbidden',
  },
};

// Spanish translations
const esTranslations: Translations = {
  common: {
    loading: 'Cargando...',
    error: 'Error',
    success: 'Éxito',
    cancel: 'Cancelar',
    confirm: 'Confirmar',
    save: 'Guardar',
    delete: 'Eliminar',
    edit: 'Editar',
    view: 'Ver',
    search: 'Buscar',
    filter: 'Filtrar',
    clear: 'Limpiar',
    back: 'Atrás',
    next: 'Siguiente',
    previous: 'Anterior',
    close: 'Cerrar',
    submit: 'Enviar',
    retry: 'Reintentar',
  },
  navigation: {
    home: 'Inicio',
    dashboard: 'Panel',
    bookDelivery: 'Reservar Entrega',
    trackPackage: 'Rastrear Paquete',
    login: 'Iniciar Sesión',
    register: 'Registrarse',
    logout: 'Cerrar Sesión',
    profile: 'Perfil',
    settings: 'Configuración',
    help: 'Ayuda',
    contact: 'Contacto',
  },
  auth: {
    loginTitle: 'Inicia sesión en tu cuenta',
    registerTitle: 'Crea tu cuenta',
    email: 'Correo Electrónico',
    password: 'Contraseña',
    confirmPassword: 'Confirmar Contraseña',
    name: 'Nombre Completo',
    phone: 'Número de Teléfono',
    forgotPassword: '¿Olvidaste tu contraseña?',
    rememberMe: 'Recordarme',
    alreadyHaveAccount: '¿Ya tienes una cuenta?',
    dontHaveAccount: '¿No tienes una cuenta?',
    loginSuccess: 'Inicio de sesión exitoso',
    loginError: 'Error en el inicio de sesión',
    registerSuccess: 'Registro exitoso',
    registerError: 'Error en el registro',
  },
  delivery: {
    bookDelivery: 'Reservar Entrega',
    trackPackage: 'Rastrear Paquete',
    pickupAddress: 'Dirección de Recogida',
    deliveryAddress: 'Dirección de Entrega',
    packageType: 'Tipo de Paquete',
    packageSize: 'Tamaño del Paquete',
    packageWeight: 'Peso del Paquete',
    deliveryTime: 'Tiempo de Entrega',
    specialInstructions: 'Instrucciones Especiales',
    paymentMethod: 'Método de Pago',
    trackingId: 'ID de Seguimiento',
    status: 'Estado',
    estimatedDelivery: 'Entrega Estimada',
    deliveryPartner: 'Socio de Entrega',
    cost: 'Costo',
    timeline: 'Cronología',
  },
  status: {
    pending: 'Pendiente',
    confirmed: 'Confirmado',
    pickedUp: 'Recogido',
    inTransit: 'En Tránsito',
    delivered: 'Entregado',
    cancelled: 'Cancelado',
  },
  errors: {
    required: 'Este campo es obligatorio',
    invalidEmail: 'Por favor ingresa un email válido',
    invalidPhone: 'Por favor ingresa un número de teléfono válido',
    passwordTooShort: 'La contraseña debe tener al menos 8 caracteres',
    passwordsNotMatch: 'Las contraseñas no coinciden',
    networkError: 'Error de red',
    serverError: 'Error del servidor',
    notFound: 'Recurso no encontrado',
    unauthorized: 'Acceso no autorizado',
    forbidden: 'Acceso prohibido',
  },
};

// All translations
const translations: Record<Language, Translations> = {
  en: enTranslations,
  es: esTranslations,
  fr: enTranslations, // TODO: Add French translations
  de: enTranslations, // TODO: Add German translations
  hi: enTranslations, // TODO: Add Hindi translations
  zh: enTranslations, // TODO: Add Chinese translations
};

// Language metadata
export const languageMetadata: Record<Language, { name: string; nativeName: string; flag: string }> = {
  en: { name: 'English', nativeName: 'English', flag: '🇺🇸' },
  es: { name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
  fr: { name: 'French', nativeName: 'Français', flag: '🇫🇷' },
  de: { name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' },
  hi: { name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳' },
  zh: { name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
};

// I18n context
interface I18nContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  translations: Translations;
  availableLanguages: Language[];
  isRTL: boolean;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

// I18n Provider
export const I18nProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [language, setLanguage] = useState<Language>(() => {
    // Get language from localStorage or browser
    const saved = localStorage.getItem('quickdrop-language') as Language;
    if (saved && Object.keys(translations).includes(saved)) {
      return saved;
    }
    
    // Detect browser language
    const browserLang = navigator.language.split('-')[0] as Language;
    if (Object.keys(translations).includes(browserLang)) {
      return browserLang;
    }
    
    return 'en';
  });

  const changeLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('quickdrop-language', lang);
    
    // Update document language
    document.documentElement.lang = lang;
    
    // Update document direction for RTL languages
    const rtlLanguages: Language[] = []; // Add RTL languages here
    document.documentElement.dir = rtlLanguages.includes(lang) ? 'rtl' : 'ltr';
  };

  // Translation function with nested key support
  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations[language];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key; // Return key if translation not found
  };

  const contextValue: I18nContextType = {
    language,
    setLanguage: changeLanguage,
    t,
    translations: translations[language],
    availableLanguages: Object.keys(translations) as Language[],
    isRTL: false, // Update based on language
  };

  useEffect(() => {
    changeLanguage(language);
  }, [language]);

  return (
    <I18nContext.Provider value={contextValue}>
      {children}
    </I18nContext.Provider>
  );
};

// Hook to use i18n
export const useI18n = (): I18nContextType => {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider');
  }
  return context;
};

// Language selector component
export const LanguageSelector: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { language, setLanguage, availableLanguages } = useI18n();

  return (
    <select
      value={language}
      onChange={(e) => setLanguage(e.target.value as Language)}
      className={`bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-900 dark:text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 ${className}`}
    >
      {availableLanguages.map((lang) => (
        <option key={lang} value={lang}>
          {languageMetadata[lang].flag} {languageMetadata[lang].nativeName}
        </option>
      ))}
    </select>
  );
};

export default useI18n;
