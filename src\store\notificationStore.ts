import { create } from 'zustand';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  actionUrl?: string;
  actionText?: string;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  
  // Actions
  addNotification: (notification: Omit<Notification, 'id' | 'isRead' | 'createdAt'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  getUnreadCount: () => number;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,

  addNotification: (notificationData) => {
    const newNotification: Notification = {
      ...notificationData,
      id: 'notif-' + Date.now() + Math.random().toString(36).substr(2, 9),
      isRead: false,
      createdAt: new Date().toISOString()
    };

    set(state => {
      const updatedNotifications = [newNotification, ...state.notifications];
      return {
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length
      };
    });
  },

  markAsRead: (id: string) => {
    set(state => {
      const updatedNotifications = state.notifications.map(notification =>
        notification.id === id ? { ...notification, isRead: true } : notification
      );
      return {
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length
      };
    });
  },

  markAllAsRead: () => {
    set(state => ({
      notifications: state.notifications.map(notification => ({
        ...notification,
        isRead: true
      })),
      unreadCount: 0
    }));
  },

  removeNotification: (id: string) => {
    set(state => {
      const updatedNotifications = state.notifications.filter(notification => notification.id !== id);
      return {
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length
      };
    });
  },

  clearAll: () => {
    set({
      notifications: [],
      unreadCount: 0
    });
  },

  getUnreadCount: () => {
    const { notifications } = get();
    return notifications.filter(n => !n.isRead).length;
  }
}));

// Helper function to create common notification types
export const createNotification = {
  success: (title: string, message: string, actionUrl?: string, actionText?: string): Omit<Notification, 'id' | 'isRead' | 'createdAt'> => ({
    type: 'success',
    title,
    message,
    actionUrl,
    actionText
  }),

  error: (title: string, message: string, actionUrl?: string, actionText?: string): Omit<Notification, 'id' | 'isRead' | 'createdAt'> => ({
    type: 'error',
    title,
    message,
    actionUrl,
    actionText
  }),

  warning: (title: string, message: string, actionUrl?: string, actionText?: string): Omit<Notification, 'id' | 'isRead' | 'createdAt'> => ({
    type: 'warning',
    title,
    message,
    actionUrl,
    actionText
  }),

  info: (title: string, message: string, actionUrl?: string, actionText?: string): Omit<Notification, 'id' | 'isRead' | 'createdAt'> => ({
    type: 'info',
    title,
    message,
    actionUrl,
    actionText
  })
};
