import React, { useState, useEffect } from 'react';
import { Package, Clock, MapPin, Star, Calendar, Plus, Search, Filter, CheckCircle, Truck, Phone, Bell, Eye, TrendingUp } from 'lucide-react';
import { useAuthStore } from '../store/authStore';
import { useDeliveryStore } from '../store/deliveryStore';
import { useNotificationStore } from '../store/notificationStore';
import { Link } from 'react-router-dom';
import { FadeIn, StaggerContainer, StaggerItem, HoverAnimation } from '../components/AnimationComponents';
import { SearchBar, QuickFilters, packageStatusFilters } from '../components/SearchAndFilter';
import { Modal } from '../components/Modal';
import LoadingSpinner, { SkeletonCard } from '../components/LoadingSpinner';
import { showToast } from '../components/Toast';

const UserDashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { getUserPackages, isLoading } = useDeliveryStore();
  const { notifications, unreadCount } = useNotificationStore();

  const [activeTab, setActiveTab] = useState('recent');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilters, setStatusFilters] = useState<string[]>([]);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [showPackageModal, setShowPackageModal] = useState(false);

  // Get user's packages
  const userPackages = user ? getUserPackages(user.id) : [];

  // Filter packages based on search and filters
  const filteredPackages = userPackages.filter(pkg => {
    const matchesSearch = pkg.trackingId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         pkg.pickupAddress.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         pkg.deliveryAddress.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilters.length === 0 || statusFilters.includes(pkg.status);

    return matchesSearch && matchesStatus;
  });

  // Calculate stats
  const stats = {
    total: userPackages.length,
    delivered: userPackages.filter(p => p.status === 'delivered').length,
    inTransit: userPackages.filter(p => p.status === 'in_transit').length,
    pending: userPackages.filter(p => p.status === 'pending').length,
  };

  // Mock data
  const recentOrders = [
    {
      id: 'QD12345678',
      status: 'Delivered',
      date: '2024-01-15',
      pickup: '123 Main St',
      delivery: '456 Oak Ave',
      cost: '₹90',
      rating: 5
    },
    {
      id: 'QD87654321',
      status: 'In Transit',
      date: '2024-01-16',
      pickup: '789 Pine St',
      delivery: '321 Elm Rd',
      cost: '₹75',
      rating: null
    },
    {
      id: 'QD13579246',
      status: 'Delivered',
      date: '2024-01-14',
      pickup: '555 Cedar Ln',
      delivery: '777 Maple Dr',
      cost: '₹120',
      rating: 4
    }
  ];

  const savedAddresses = [
    {
      id: 1,
      label: 'Home',
      address: '123 Main Street, Downtown, City - 400001',
      isDefault: true
    },
    {
      id: 2,
      label: 'Office',
      address: '456 Business Park, Commercial Area, City - 400020',
      isDefault: false
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered':
        return 'bg-green-100 text-green-800';
      case 'In Transit':
        return 'bg-blue-100 text-blue-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <FadeIn>
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800">Welcome back, {user?.name}!</h1>
            <p className="text-gray-600 mt-2">Manage your deliveries and account settings</p>
            {unreadCount > 0 && (
              <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Bell className="h-5 w-5 text-blue-600" />
                  <span className="text-blue-800 font-medium">
                    You have {unreadCount} new notification{unreadCount > 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            )}
          </div>
        </FadeIn>

        {/* Stats Cards */}
        <StaggerContainer className="grid md:grid-cols-4 gap-6 mb-8">
          <StaggerItem>
            <HoverAnimation>
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Package className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Total Orders</h3>
                    <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
                  </div>
                </div>
              </div>
            </HoverAnimation>
          </StaggerItem>

          <StaggerItem>
            <HoverAnimation>
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-3 rounded-full">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Delivered</h3>
                    <p className="text-2xl font-bold text-green-600">{stats.delivered}</p>
                  </div>
                </div>
              </div>
            </HoverAnimation>
          </StaggerItem>

          <StaggerItem>
            <HoverAnimation>
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-yellow-100 p-3 rounded-full">
                    <Truck className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">In Transit</h3>
                    <p className="text-2xl font-bold text-yellow-600">{stats.inTransit}</p>
                  </div>
                </div>
              </div>
            </HoverAnimation>
          </StaggerItem>

          <StaggerItem>
            <HoverAnimation>
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-gray-100 p-3 rounded-full">
                    <Clock className="h-6 w-6 text-gray-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">Pending</h3>
                    <p className="text-2xl font-bold text-gray-600">{stats.pending}</p>
                  </div>
                </div>
              </div>
            </HoverAnimation>
          </StaggerItem>
        </StaggerContainer>

        {/* Quick Actions */}
        <StaggerContainer className="grid md:grid-cols-3 gap-6 mb-8">
          <StaggerItem>
            <HoverAnimation>
              <Link to="/book" className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all block">
                <div className="flex items-center space-x-3">
                  <Plus className="h-8 w-8" />
                  <div>
                    <h3 className="font-semibold text-lg">Book Delivery</h3>
                    <p className="text-blue-100">Schedule a new pickup</p>
                  </div>
                </div>
              </Link>
            </HoverAnimation>
          </StaggerItem>

          <StaggerItem>
            <HoverAnimation>
              <Link to="/track" className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all border border-gray-200 block">
                <div className="flex items-center space-x-3">
                  <Search className="h-8 w-8 text-blue-600" />
                  <div>
                    <h3 className="font-semibold text-lg text-gray-800">Track Package</h3>
                    <p className="text-gray-600">Monitor your deliveries</p>
                  </div>
                </div>
              </Link>
            </HoverAnimation>
          </StaggerItem>

          <StaggerItem>
            <HoverAnimation>
              <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
                <div className="flex items-center space-x-3">
                  <TrendingUp className="h-8 w-8 text-green-600" />
                  <div>
                    <h3 className="font-semibold text-lg text-gray-800">Success Rate</h3>
                    <p className="text-2xl font-bold text-green-600">98.5%</p>
                  </div>
                </div>
              </div>
            </HoverAnimation>
          </StaggerItem>
        </StaggerContainer>

        {/* Enhanced Tabs with Search and Filters */}
        <FadeIn delay={0.3}>
          <div className="bg-white rounded-xl shadow-lg">
            <div className="border-b border-gray-200">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4">
                <nav className="flex space-x-8">
                  {[
                    { id: 'recent', label: 'Recent Orders', icon: Clock },
                    { id: 'addresses', label: 'Saved Addresses', icon: MapPin }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 py-2 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700'
                      }`}
                    >
                      <tab.icon className="h-5 w-5" />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </nav>

                {activeTab === 'recent' && (
                  <div className="flex flex-col sm:flex-row gap-4 mt-4 lg:mt-0">
                    <SearchBar
                      placeholder="Search orders..."
                      value={searchQuery}
                      onChange={setSearchQuery}
                      className="w-full sm:w-64"
                    />
                    <QuickFilters
                      filters={packageStatusFilters}
                      selectedFilters={statusFilters}
                      onChange={setStatusFilters}
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="p-6">
              {/* Recent Orders Tab */}
              {activeTab === 'recent' && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-800">
                      Your Orders ({filteredPackages.length})
                    </h3>
                    <Link
                      to="/book"
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      Book New Delivery
                    </Link>
                  </div>

                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(3)].map((_, i) => (
                        <SkeletonCard key={i} className="h-32" />
                      ))}
                    </div>
                  ) : filteredPackages.length === 0 ? (
                    <div className="text-center py-12">
                      <Package className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
                      <p className="text-gray-500 mb-6">
                        {userPackages.length === 0
                          ? "You haven't placed any orders yet."
                          : "No orders match your current filters."
                        }
                      </p>
                      <Link
                        to="/book"
                        className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      >
                        Book Your First Delivery
                      </Link>
                    </div>
                  ) : (
                    <StaggerContainer className="space-y-4">
                      {filteredPackages.map((pkg) => (
                        <StaggerItem key={pkg.id}>
                          <HoverAnimation>
                            <div className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors border border-gray-200">
                              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                                <div className="flex-1">
                                  <div className="flex items-center space-x-4 mb-3">
                                    <span className="font-semibold text-gray-800">#{pkg.trackingId}</span>
                                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(pkg.status)}`}>
                                      {pkg.status.replace('_', ' ').toUpperCase()}
                                    </span>
                                    <span className="text-sm text-gray-500">
                                      <Calendar className="h-4 w-4 inline mr-1" />
                                      {new Date(pkg.createdAt).toLocaleDateString()}
                                    </span>
                                  </div>

                                  <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                                    <div>
                                      <span className="font-medium">From:</span> {pkg.pickupAddress}
                                    </div>
                                    <div>
                                      <span className="font-medium">To:</span> {pkg.deliveryAddress}
                                    </div>
                                  </div>

                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <span>Type: {pkg.packageType}</span>
                                    <span>Size: {pkg.packageSize}</span>
                                    {pkg.deliveryPartner && (
                                      <span>Partner: {pkg.deliveryPartner.name}</span>
                                    )}
                                  </div>
                                </div>

                                <div className="mt-4 lg:mt-0 lg:text-right">
                                  <div className="text-lg font-semibold text-gray-800">₹{pkg.cost}</div>
                                  {pkg.deliveryPartner && (
                                    <div className="flex items-center justify-end space-x-1 mt-1">
                                      {[...Array(5)].map((_, i) => (
                                        <Star
                                          key={i}
                                          className={`h-4 w-4 ${
                                            i < pkg.deliveryPartner!.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                          }`}
                                        />
                                      ))}
                                    </div>
                                  )}
                                  <div className="flex space-x-2 mt-3">
                                    <button
                                      onClick={() => {
                                        setSelectedPackage(pkg);
                                        setShowPackageModal(true);
                                      }}
                                      className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1"
                                    >
                                      <Eye className="h-4 w-4" />
                                      <span>View Details</span>
                                    </button>
                                    {(pkg.status === 'in_transit' || pkg.status === 'picked_up') && (
                                      <Link
                                        to={`/track?id=${pkg.trackingId}`}
                                        className="text-green-600 hover:text-green-700 text-sm font-medium"
                                      >
                                        Track Live
                                      </Link>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </HoverAnimation>
                        </StaggerItem>
                      ))}
                    </StaggerContainer>
                  )}
                </div>
              )}

              {/* Saved Addresses Tab */}
              {activeTab === 'addresses' && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-800">Saved Addresses</h3>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                      Add Address
                    </button>
                  </div>

                  <div className="text-center py-12">
                    <MapPin className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No saved addresses</h3>
                    <p className="text-gray-500 mb-6">
                      Save your frequently used addresses for faster booking.
                    </p>
                    <button className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                      Add Your First Address
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Package Details Modal */}
            <Modal
              isOpen={showPackageModal}
              onClose={() => setShowPackageModal(false)}
              title="Package Details"
              size="lg"
            >
              {selectedPackage && (
                <div className="p-6 space-y-6">
                  {/* Package Info */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2">Package Information</h4>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Tracking ID:</span> {selectedPackage.trackingId}</div>
                          <div><span className="font-medium">Type:</span> {selectedPackage.packageType}</div>
                          <div><span className="font-medium">Size:</span> {selectedPackage.packageSize}</div>
                          <div><span className="font-medium">Weight:</span> {selectedPackage.packageWeight}kg</div>
                          <div><span className="font-medium">Status:</span>
                            <span className={`ml-2 px-2 py-1 rounded-full text-xs ${getStatusColor(selectedPackage.status)}`}>
                              {selectedPackage.status.replace('_', ' ').toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-800 mb-2">Delivery Details</h4>
                        <div className="space-y-2 text-sm">
                          <div><span className="font-medium">Cost:</span> ₹{selectedPackage.cost}</div>
                          <div><span className="font-medium">Payment:</span> {selectedPackage.paymentMethod}</div>
                          <div><span className="font-medium">Created:</span> {new Date(selectedPackage.createdAt).toLocaleString()}</div>
                          {selectedPackage.estimatedDelivery && (
                            <div><span className="font-medium">ETA:</span> {selectedPackage.estimatedDelivery}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Addresses */}
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="bg-green-50 rounded-lg p-4">
                      <h4 className="font-semibold text-green-800 mb-2 flex items-center">
                        <MapPin className="h-4 w-4 mr-2" />
                        Pickup Address
                      </h4>
                      <p className="text-sm text-green-700">{selectedPackage.pickupAddress}</p>
                      <p className="text-xs text-green-600 mt-1">Contact: {selectedPackage.pickupContact}</p>
                    </div>
                    <div className="bg-blue-50 rounded-lg p-4">
                      <h4 className="font-semibold text-blue-800 mb-2 flex items-center">
                        <MapPin className="h-4 w-4 mr-2" />
                        Delivery Address
                      </h4>
                      <p className="text-sm text-blue-700">{selectedPackage.deliveryAddress}</p>
                      <p className="text-xs text-blue-600 mt-1">Contact: {selectedPackage.deliveryContact}</p>
                    </div>
                  </div>

                  {/* Delivery Partner */}
                  {selectedPackage.deliveryPartner && (
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-semibold text-gray-800 mb-3">Delivery Partner</h4>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="bg-blue-100 p-2 rounded-full">
                            <Truck className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium text-gray-800">{selectedPackage.deliveryPartner.name}</p>
                            <div className="flex items-center space-x-1">
                              <Star className="h-4 w-4 text-yellow-400 fill-current" />
                              <span className="text-sm text-gray-600">{selectedPackage.deliveryPartner.rating}/5.0</span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => window.open(`tel:${selectedPackage.deliveryPartner?.phone}`)}
                          className="flex items-center space-x-2 bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                        >
                          <Phone className="h-4 w-4" />
                          <span>Call</span>
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Timeline */}
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-3">Timeline</h4>
                    <div className="space-y-3">
                      {selectedPackage.timeline.map((event: any, index: number) => (
                        <div key={index} className="flex items-start space-x-3">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center bg-green-100 text-green-600">
                            <CheckCircle className="h-4 w-4" />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-800">{event.status.replace('_', ' ').toUpperCase()}</p>
                            <p className="text-sm text-gray-600">{event.description}</p>
                            <p className="text-xs text-gray-500">{new Date(event.timestamp).toLocaleString()}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-3 pt-4 border-t border-gray-200">
                    <Link
                      to={`/track?id=${selectedPackage.trackingId}`}
                      className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium"
                      onClick={() => setShowPackageModal(false)}
                    >
                      Track Package
                    </Link>
                    <button
                      onClick={() => setShowPackageModal(false)}
                      className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                    >
                      Close
                    </button>
                  </div>
                </div>
              )}
            </Modal>
          </div>
        </FadeIn>
      </div>
    </div>
  );
};

export default UserDashboard;